"use client";

import * as React from "react"
import { type LucideIcon } from "lucide-react"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { FeedbackDialog } from "./feedback-dialog"

interface NavSecondaryItem {
  title: string
  url?: string
  icon: LucideIcon
  type?: "link" | "dialog"
}

export function NavSecondary({
  items,
  ...props
}: {
  items: NavSecondaryItem[]
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              {item.type === "dialog" && item.title === "Feedback" ? (
                <FeedbackDialog
                  trigger={
                    <SidebarMenuButton size="sm" className="transition-all duration-200 ease-in-out  hover:text-primary hover:scale-[1.02] hover:font-medium hover:shadow-sm dark:hover:bg-primary/20 rounded-md cursor-pointer">
                      <item.icon />
                      <span>{item.title}</span>
                    </SidebarMenuButton>
                  }
                />
              ) : (
                <SidebarMenuButton asChild size="sm">
                  <a href={item.url} className="transition-all duration-200 ease-in-out  hover:text-primary hover:scale-[1.02] hover:font-medium hover:shadow-sm dark:hover:bg-primary/20 rounded-md">
                    <item.icon />
                    <span>{item.title}</span>
                  </a>
                </SidebarMenuButton>
              )}
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
