"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import { doc, getDoc, collection, query, where, onSnapshot, serverTimestamp, DocumentData, Timestamp, deleteDoc, updateDoc } from "firebase/firestore";
import { db, functions, storage } from "@/app/firebase";
import { httpsCallable } from "firebase/functions";
import { Building2, ShieldCheck, Users, Mail, Info, PlusCircle, ChevronRight, X, Check, Clock, Trash2, ExternalLink, Pencil, MapPin, AlertTriangle, Settings } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";
import { UserProfile, Invitation, Address } from "@/types";
import { motion } from "framer-motion";
import { format } from "date-fns";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";

interface OrgUser {
  id: string;
  role: string;
  userId: string;
  profile: UserProfile | null;
  lastAccessed?: any;
}

interface PendingInvitation extends Invitation {
  id: string;
}

// Animation variants for staggered animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 120,
      damping: 20
    }
  }
};

export default function OrganizationManagePage() {
  const router = useRouter();
  const { user, activeOrganizationId, claims } = useAuth();
  const [organization, setOrganization] = useState<any>(null);
  const [users, setUsers] = useState<OrgUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState("viewer");
  const [isCreating, setIsCreating] = useState(false);
  const [pendingInvitations, setPendingInvitations] = useState<PendingInvitation[]>([]);
  const [isDeletingInvitation, setIsDeletingInvitation] = useState<string | null>(null);

  // Organization deletion state
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Organization editing state
  const [isEditingOrgDetails, setIsEditingOrgDetails] = useState(false);
  const [editedOrgName, setEditedOrgName] = useState("");
  const [editedOrgEmail, setEditedOrgEmail] = useState("");
  const [editedOrgDescription, setEditedOrgDescription] = useState("");
  const [editedOrgWebsite, setEditedOrgWebsite] = useState("");
  const [editedOrgPhone, setEditedOrgPhone] = useState("");
  const [isSavingOrgDetails, setIsSavingOrgDetails] = useState(false);

  const isAdmin = claims?.admin || false;

  useEffect(() => {
    if (!activeOrganizationId) return;

    setLoading(true);
    const fetchOrg = async () => {
      try {
        const orgDocRef = doc(db, "organizations", activeOrganizationId);
        const orgDoc = await getDoc(orgDocRef);

        if (orgDoc.exists()) {
          setOrganization({ id: orgDoc.id, ...orgDoc.data() });
        } else {
          toast.error("Organization not found");
          setOrganization(null);
        }
      } catch (error) {
        console.error("Error fetching organization:", error);
        toast.error("Error loading organization");
        setOrganization(null);
      } finally {
        setLoading(false);
      }
    };

    fetchOrg();

    const userOrgsRef = collection(db, "userOrganizations");
    const q = query(userOrgsRef, where("organizationId", "==", activeOrganizationId));

    const unsubscribeUsers = onSnapshot(q, async (snapshot) => {
      try {
        const userDataPromises = snapshot.docs.map(async (docSnapshot) => {
          const data = docSnapshot.data();

          try {
            const profileRef = doc(db, "userProfiles", data.userId);
            const profileSnap = await getDoc(profileRef);

            return {
              id: docSnapshot.id,
              userId: data.userId,
              role: data.role || "viewer",
              lastAccessed: data.lastAccessed,
              profile: profileSnap.exists() ? profileSnap.data() as UserProfile : null,
            };
          } catch (profileError) {
            console.error(`Error fetching profile for user ${data.userId}:`, profileError);
            return {
              id: docSnapshot.id,
              userId: data.userId,
              role: data.role || "viewer",
              lastAccessed: data.lastAccessed,
              profile: null,
            };
          }
        });

        const resolvedUsers = await Promise.all(userDataPromises);
        setUsers(resolvedUsers);
      } catch (error) {
        console.error("Error processing organization users snapshot:", error);
        setUsers([]);
      }
    }, (error) => {
      console.error("Error listening to organization users:", error);
      setUsers([]);
      toast.error("Error loading member list.");
    });

    // Fetch pending invitations
    const invitationsRef = collection(db, "invitations");
    const invitationsQuery = query(
      invitationsRef,
      where("organizationId", "==", activeOrganizationId),
      where("status", "==", "pending")
    );

    const unsubscribeInvitations = onSnapshot(invitationsQuery, (snapshot) => {
      try {
        const invitations = snapshot.docs.map(doc => {
          const data = doc.data() as Invitation;
          return { id: doc.id, ...data };
        });
        setPendingInvitations(invitations);
      } catch (error) {
        console.error("Error fetching invitations:", error);
        setPendingInvitations([]);
      }
    });

    return () => {
      unsubscribeUsers();
      unsubscribeInvitations();
    };
  }, [activeOrganizationId]);

  const createInvite = async () => {
    if (!inviteEmail.trim() || !activeOrganizationId) return;

    setIsCreating(true);
    try {
      const createInvitation = httpsCallable(functions, "createInvitation");
      await createInvitation({
        email: inviteEmail.trim(),
        role: inviteRole,
        organizationId: activeOrganizationId,
      });

      setInviteEmail("");
      setInviteRole("viewer");
      toast.success("Invitation sent successfully");
    } catch (error: any) {
      console.error("Error creating invitation:", error);
      toast.error(error.message || "Error creating invitation");
    } finally {
      setIsCreating(false);
    }
  };

  const cancelInvitation = async (invitationId: string) => {
    setIsDeletingInvitation(invitationId);
    try {
      await deleteDoc(doc(db, "invitations", invitationId));
      toast.success("Invitation cancelled");
    } catch (error) {
      console.error("Error cancelling invitation:", error);
      toast.error("Error cancelling invitation");
    } finally {
      setIsDeletingInvitation(null);
    }
  };

  const formatExpiryDate = (timestamp: any) => {
    if (!timestamp) return "Unknown";
    const date = timestamp instanceof Timestamp ? timestamp.toDate() : new Date(timestamp);
    return format(date, "PPP");
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "admin": return "destructive";
      case "viewer": return "secondary";
      default: return "secondary";
    }
  };

  const getInitials = (displayName?: string) => {
    if (!displayName) return "?";
    return displayName.split(" ").map(name => name[0]).join("").toUpperCase().slice(0, 2);
  };

  const startEditingOrgDetails = () => {
    if (organization) {
      setEditedOrgName(organization.name || "");
      setEditedOrgEmail(organization.contactEmail || "");
      setEditedOrgDescription(organization.description || "");
      setEditedOrgWebsite(organization.website || "");
      setEditedOrgPhone(organization.phone || "");
      setIsEditingOrgDetails(true);
    }
  };

  const cancelEditingOrgDetails = () => {
    setIsEditingOrgDetails(false);
  };

  const saveOrgDetails = async () => {
    if (!activeOrganizationId) return;

    setIsSavingOrgDetails(true);
    try {
      const orgRef = doc(db, "organizations", activeOrganizationId);
      await updateDoc(orgRef, {
        name: editedOrgName,
        contactEmail: editedOrgEmail,
        description: editedOrgDescription,
        website: editedOrgWebsite,
        phone: editedOrgPhone,
        updatedAt: serverTimestamp(),
      });

      setIsEditingOrgDetails(false);
      toast.success("Organization updated successfully");
    } catch (error) {
      console.error("Error updating organization:", error);
      toast.error("Error updating organization");
    } finally {
      setIsSavingOrgDetails(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <div className="grid gap-6 md:grid-cols-2">
            <Skeleton className="h-64" />
            <Skeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Organization Not Found</h2>
            <p className="text-muted-foreground">
              The organization you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-8"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Organization Settings</h1>
            <p className="text-muted-foreground">
              Manage your organization settings and members
            </p>
          </div>
        </motion.div>

        {/* Organization Details */}
        <motion.div variants={itemVariants}>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Organization Details
                </CardTitle>
                <CardDescription>
                  Basic information about your organization
                </CardDescription>
              </div>
              {isAdmin && !isEditingOrgDetails && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={startEditingOrgDetails}
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              {isEditingOrgDetails ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="orgName">Name</Label>
                      <Input
                        id="orgName"
                        value={editedOrgName}
                        onChange={(e) => setEditedOrgName(e.target.value)}
                        placeholder="Organization name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="orgEmail">Contact Email</Label>
                      <Input
                        id="orgEmail"
                        type="email"
                        value={editedOrgEmail}
                        onChange={(e) => setEditedOrgEmail(e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="orgWebsite">Website</Label>
                      <Input
                        id="orgWebsite"
                        value={editedOrgWebsite}
                        onChange={(e) => setEditedOrgWebsite(e.target.value)}
                        placeholder="https://organization.com"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="orgPhone">Phone</Label>
                      <Input
                        id="orgPhone"
                        value={editedOrgPhone}
                        onChange={(e) => setEditedOrgPhone(e.target.value)}
                        placeholder="+****************"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="orgDescription">Description</Label>
                    <Input
                      id="orgDescription"
                      value={editedOrgDescription}
                      onChange={(e) => setEditedOrgDescription(e.target.value)}
                      placeholder="Brief description of your organization"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={saveOrgDetails}
                      disabled={isSavingOrgDetails}
                    >
                      {isSavingOrgDetails ? "Saving..." : "Save Changes"}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={cancelEditingOrgDetails}
                      disabled={isSavingOrgDetails}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Name</Label>
                    <p className="mt-1">{organization.name || "Not set"}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Contact Email</Label>
                    <p className="mt-1">{organization.contactEmail || "Not set"}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Website</Label>
                    <p className="mt-1">{organization.website || "Not set"}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Phone</Label>
                    <p className="mt-1">{organization.phone || "Not set"}</p>
                  </div>
                  <div className="md:col-span-2">
                    <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                    <p className="mt-1">{organization.description || "No description provided"}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Members Management */}
        <motion.div variants={itemVariants}>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Members ({users.length})
              </CardTitle>
              <CardDescription>
                Manage organization members and their roles
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Invite Section */}
              {isAdmin && (
                <div className="border rounded-lg p-4 space-y-4">
                  <h3 className="font-medium">Invite New Member</h3>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Email address"
                      type="email"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                      className="flex-1"
                    />
                    <Select value={inviteRole} onValueChange={setInviteRole}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="viewer">Viewer</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button
                      onClick={createInvite}
                      disabled={!inviteEmail.trim() || isCreating}
                    >
                      {isCreating ? "Sending..." : "Invite"}
                    </Button>
                  </div>
                </div>
              )}

              {/* Current Members */}
              <div className="space-y-2">
                {users.map((orgUser) => (
                  <div
                    key={orgUser.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback>
                          {getInitials(orgUser.profile?.displayName)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">
                          {orgUser.profile?.displayName || "Unknown User"}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {orgUser.profile?.email}
                        </p>
                      </div>
                    </div>
                    <Badge variant={getRoleBadgeVariant(orgUser.role)}>
                      {orgUser.role}
                    </Badge>
                  </div>
                ))}
              </div>

              {/* Pending Invitations */}
              {pendingInvitations.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm text-muted-foreground">
                    Pending Invitations ({pendingInvitations.length})
                  </h4>
                  {pendingInvitations.map((invitation) => (
                    <div
                      key={invitation.id}
                      className="flex items-center justify-between p-3 border rounded-lg bg-muted/30"
                    >
                      <div className="flex items-center gap-3">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{invitation.email}</p>
                          <p className="text-sm text-muted-foreground">
                            Expires: {formatExpiryDate(invitation.expiresAt)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getRoleBadgeVariant(invitation.role)}>
                          {invitation.role}
                        </Badge>
                        {isAdmin && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => cancelInvitation(invitation.id)}
                            disabled={isDeletingInvitation === invitation.id}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  );
}