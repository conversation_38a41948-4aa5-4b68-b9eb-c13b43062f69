'use client';

import { useEffect, useState } from 'react';
import { db } from '@/app/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { useAuth } from '@/components/providers/auth-provider';
import { toast } from 'sonner';
import { Loader2, GitBranch } from 'lucide-react';

// Types
export interface GitHubLabel {
  name: string;
  color: string;
}

export interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body: string | null;
  state: 'open' | 'closed';
  labels: GitHubLabel[];
  created_at: string;
  updated_at: string;
  pull_request?: any;
}

export interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
}

interface IssuesStats {
  open: number;
  closed: number;
}

interface IssuesFetcherProps {
  selectedRepo: Repository;
  onIssuesContent: (content: string) => void;
}

// Helper functions
function formatIssuePreview(issue: GitHubIssue, bodyLength: number): string {
  const labels = issue.labels.map(label => label.name).join(', ');
  let formatted = `- #${issue.number}: ${issue.title}`;
  
  if (labels) formatted += ` [${labels}]`;
  
  if (issue.body && issue.body.length > 0) {
    const bodyPreview = issue.body.substring(0, bodyLength).replace(/\n/g, ' ');
    formatted += ` - ${bodyPreview}${issue.body.length > bodyLength ? '...' : ''}`;
  }
  
  return formatted + '\n';
}

function formatIssuesForContext(openIssues: GitHubIssue[], closedIssues: GitHubIssue[]): string {
  let context = '';

  if (openIssues.length > 0) {
    context += `\n\nOFFENE GITHUB ISSUES (${openIssues.length}):\n`;
    openIssues.forEach(issue => {
      context += formatIssuePreview(issue, 200);
    });
  }

  if (closedIssues.length > 0) {
    context += `\n\nGESCHLOSSENE GITHUB ISSUES (${closedIssues.length}):\n`;
    closedIssues.forEach(issue => {
      context += formatIssuePreview(issue, 150);
    });
  }

  return context;
}

// Custom hook for fetching GitHub issues
function useGitHubIssues(user: any, selectedRepo: Repository | null) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [issuesStats, setIssuesStats] = useState<IssuesStats | null>(null);
  const [issuesContext, setIssuesContext] = useState('');

  useEffect(() => {
    async function fetchIssues() {
      if (!user || !selectedRepo) {
        setIssuesStats(null);
        setError(null);
        setIssuesContext('');
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the GitHub token from Firestore
        const userProfileRef = doc(db, "userProfiles", user.uid);
        const userProfileSnap = await getDoc(userProfileRef);

        if (!userProfileSnap.exists() || !userProfileSnap.data().githubToken) {
          setError("Kein GitHub-Token gefunden. Bitte melden Sie sich erneut an.");
          return;
        }

        const githubToken = userProfileSnap.data().githubToken;
        const headers = {
          'Authorization': `token ${githubToken}`,
          'Accept': 'application/vnd.github.v3+json'
        };

        // Fetch both open and closed issues
        const [openResponse, closedResponse] = await Promise.all([
          fetch(`https://api.github.com/repos/${selectedRepo.full_name}/issues?state=open&per_page=100`, { headers }),
          fetch(`https://api.github.com/repos/${selectedRepo.full_name}/issues?state=closed&per_page=100`, { headers })
        ]);

        if (!openResponse.ok || !closedResponse.ok) {
          throw new Error(`GitHub API error: ${openResponse.status} / ${closedResponse.status}`);
        }

        const [openIssues, closedIssues]: [GitHubIssue[], GitHubIssue[]] = await Promise.all([
          openResponse.json(),
          closedResponse.json()
        ]);

        // Filter out pull requests (GitHub API returns both issues and PRs)
        const filteredOpenIssues = openIssues.filter(issue => !issue.pull_request);
        const filteredClosedIssues = closedIssues.filter(issue => !issue.pull_request);

        // Create formatted context for the AI
        const formattedContext = formatIssuesForContext(filteredOpenIssues, filteredClosedIssues);
        
        setIssuesStats({
          open: filteredOpenIssues.length,
          closed: filteredClosedIssues.length
        });

        setIssuesContext(formattedContext);

      } catch (error) {
        console.error('Error fetching GitHub issues:', error);
        setError('Fehler beim Laden der GitHub Issues. Bitte versuchen Sie es erneut.');
        setIssuesStats(null);
        setIssuesContext('');
      } finally {
        setIsLoading(false);
      }
    }

    fetchIssues();
  }, [user, selectedRepo]);

  return { isLoading, error, issuesStats, issuesContext };
}

// Main component
export function IssuesFetcher({ selectedRepo, onIssuesContent }: IssuesFetcherProps) {
  const { user } = useAuth();
  const { isLoading, error, issuesStats, issuesContext } = useGitHubIssues(user, selectedRepo);

  // Update parent component with issues context when it changes
  useEffect(() => {
    onIssuesContent(issuesContext);
  }, [issuesContext, onIssuesContent]);

  if (error) {
    return (
      <div className="mt-4">
        <div className="flex items-center space-x-2 mb-2">
          <GitBranch className="h-4 w-4 text-red-500" />
          <span className="text-sm font-medium text-red-600">Fehler beim Laden der Issues</span>
        </div>
        <p className="text-xs text-red-500">{error}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="mt-4">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">Lade GitHub Issues...</span>
        </div>
      </div>
    );
  }

  if (issuesStats) {
    return (
      <div className="mt-4">
        <div className="flex items-center space-x-2 mb-2">
          <GitBranch className="h-4 w-4" />
          <span className="text-sm font-medium">GitHub Issues als Kontext geladen</span>
        </div>
        <div className="p-3 bg-muted rounded-md">
          <p className="text-xs text-muted-foreground">
            {issuesStats.open} offene, {issuesStats.closed} geschlossene Issues geladen
          </p>
        </div>
      </div>
    );
  }

  return null;
}
