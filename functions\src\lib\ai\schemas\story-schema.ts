import { z } from "genkit";

// Flattened schema with conditional validation based on isRejected
export const UserStorySchema = z.object({
  // Common fields
  isRejected: z.boolean().describe("Whether this story is rejected as a duplicate"),
  title: z.string().describe("A concise, descriptive title for the user story"),
  
  // Fields for accepted stories (optional in schema, but will be validated)
  role: z.string().optional().describe("The specific user role or persona who will benefit from this feature"),
  goal: z.string().optional().describe("What the user wants to accomplish"),
  benefit: z.string().optional().describe("The value or benefit the user will receive from this feature"),
  formattedStory: z.string().optional().describe("Complete user story in standard format: 'As a [role], I want to [goal], so that [benefit]'"),
  feasibility: z.number().min(1).max(5).optional().describe("Technical feasibility rating from 1 (very difficult) to 5 (very feasible)"),
  complexity: z.number().min(1).max(10).optional().describe("Implementation complexity from 1 (very simple) to 10 (extremely complex)"),
  priority: z.enum(["Critical", "High", "Medium", "Low"]).optional().describe("Business priority level for this feature"),
  featureCategory: z.string().optional().describe("Category or type of feature (e.g., 'Authentication', 'Reporting', 'User Management')"),
  issueAnalysis: z.string().optional().describe("Analysis of relationships to similar (but non-duplicate) existing issues"),
  
  // Fields for rejected stories (optional in schema, but will be validated)
  rejectionReason: z.string().optional().describe("Reason why this story was rejected"),
  duplicateIssueNumber: z.number().optional().describe("Issue number of the duplicate"),
  duplicateIssueTitle: z.string().optional().describe("Title of the duplicate issue"),
}).superRefine((data, ctx) => {
  // Validation logic: ensure the appropriate fields are filled based on isRejected
  if (data.isRejected === true) {
    // For rejected stories, ensure these fields are present
    if (!data.rejectionReason) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "rejectionReason is required when isRejected is true",
        path: ["rejectionReason"]
      });
    }
    if (!data.duplicateIssueNumber) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "duplicateIssueNumber is required when isRejected is true",
        path: ["duplicateIssueNumber"]
      });
    }
    if (!data.duplicateIssueTitle) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "duplicateIssueTitle is required when isRejected is true",
        path: ["duplicateIssueTitle"]
      });
    }
  } else if (data.isRejected === false) {
    // For accepted stories, ensure these fields are present
    const requiredFields = [
      "role", "goal", "benefit", "formattedStory", 
      "feasibility", "complexity", "priority", "featureCategory"
    ];
    
    for (const field of requiredFields) {
      if (!data[field as keyof typeof data]) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${field} is required when isRejected is false`,
          path: [field]
        });
      }
    }
  }
});

// Type derived directly from the schema
export type UserStory = z.infer<typeof UserStorySchema>; 