import { jest } from '@jest/globals';

// Mock the hook module
jest.mock('@/hooks/use-image-upload');

// Import the mocked hook *after* jest.mock
// We need to know the actual type/signature of useImageUpload for proper casting
// Assuming it's a function for now. Adjust the import/type if needed.
import { useImageUpload } from '@/hooks/use-image-upload';

/**
 * <PERSON><PERSON><PERSON> und Hilfsfunktionen für den useImageUpload Hook
 */
export const mockUseImageUpload = () => {
  const mockHandleFileChange = jest.fn();
  const mockHandleRemove = jest.fn();
  const mockHandleThumbnailClick = jest.fn();
  
  // Cast the imported hook to its mocked type
  const mockedUseImageUpload = useImageUpload as jest.MockedFunction<typeof useImageUpload>;
  
  // Basis-Mock-Implementierung
  const defaultMock = {
    previewUrl: null,
    fileName: null,
    fileInputRef: { current: null },
    handleThumbnailClick: mockHandleThumbnailClick,
    handleFileChange: mockHandleFileChange,
    handleRemove: mockHandleRemove,
  };
  
  // Funktion zum Überschreiben des Mocks mit benutzerdefinierten Werten
  const mockWithValues = (overrides = {}) => {
    const newMock = { ...defaultMock, ...overrides };
    // Use the mocked function directly
    mockedUseImageUpload.mockReturnValue(newMock);
    return newMock;
  };
  
  return {
    mockHandleFileChange,
    mockHandleRemove,
    mockHandleThumbnailClick,
    defaultMock,
    mockWithValues,
  };
}; 