import { initializeTestEnvironment, assertSucceeds, assertFails, RulesTestEnvironment } from '@firebase/rules-unit-testing';
import { doc, setDoc, getDoc, updateDoc, deleteDoc, Timestamp } from 'firebase/firestore';
import * as fs from 'fs';

jest.setTimeout(30000);

// Test environment and common variables
let testEnv: RulesTestEnvironment;
const projectId = 'o3domo';

const organizationId = 'test-org-123';
const anotherOrganizationId = 'another-org-456';
const managerId = 'test-manager-abc';
const adminId = 'test-admin-xyz';
const memberId = 'test-member-def';
const meterId = 'test-meter-xyz';
const readingId = 'test-reading-abc';

const meterPath = `meters/${meterId}`;

// Helper function to create auth contexts with appropriate roles
const getAuthContext = (uid: string, role: 'admin' | 'manager' | 'member', orgId: string = organizationId) => {
  return testEnv.authenticatedContext(uid, {
    activeOrganizationId: orgId,
    activeRole: role,
    organizations: { [orgId]: role },
  });
};

// Sample meter data
const createMeterData = () => ({
  organizationId,
  objectId: 'object-123',
  unitId: null,
  assignedLevel: 'Objekt',
  meterType: 'Strom',
  meterNumber: 'SN-0001',
  meterActive: true,
});

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId,
    firestore: {
      host: 'localhost',
      port: 8080,
      rules: fs.readFileSync('firestore.rules', 'utf8'),
    },
  });
});

afterEach(async () => {
  await testEnv.clearFirestore();
});

afterAll(async () => {
  await testEnv.cleanup();
});

describe('Firestore Rules for Meters', () => {
  describe('Meters Collection', () => {
    describe('As Manager', () => {
      let managerDb: any;
      let meterRef: any;

      beforeEach(() => {
        const context = getAuthContext(managerId, 'manager');
        managerDb = context.firestore();
        meterRef = doc(managerDb, meterPath);
      });

      it('should allow creating a meter', async () => {
        await assertSucceeds(setDoc(meterRef, createMeterData()));
      });

      it('should allow reading a meter', async () => {
        await setDoc(meterRef, createMeterData());
        await assertSucceeds(getDoc(meterRef));
      });

      it('should allow updating a meter', async () => {
        await setDoc(meterRef, createMeterData());
        await assertSucceeds(updateDoc(meterRef, { meterActive: false }));
      });

      it('should allow deleting a meter', async () => {
        await setDoc(meterRef, createMeterData());
        await assertSucceeds(deleteDoc(meterRef));
      });
    });

    describe('As Admin', () => {
      let adminDb: any;
      let meterRef: any;

      beforeEach(() => {
        const context = getAuthContext(adminId, 'admin');
        adminDb = context.firestore();
        meterRef = doc(adminDb, meterPath);
      });

      it('should allow creating a meter', async () => {
        await assertSucceeds(setDoc(meterRef, createMeterData()));
      });

      it('should allow reading a meter created by others', async () => {
        const managerContext = getAuthContext(managerId, 'manager');
        const managerDb = managerContext.firestore();
        await setDoc(doc(managerDb, meterPath), createMeterData());
        await assertSucceeds(getDoc(meterRef));
      });

      it('should allow updating a meter created by others', async () => {
        const managerContext = getAuthContext(managerId, 'manager');
        const managerDb = managerContext.firestore();
        await setDoc(doc(managerDb, meterPath), createMeterData());
        await assertSucceeds(updateDoc(meterRef, { meterNumber: 'SN-0002' }));
      });

      it('should allow deleting a meter created by others', async () => {
        const managerContext = getAuthContext(managerId, 'manager');
        const managerDb = managerContext.firestore();
        await setDoc(doc(managerDb, meterPath), createMeterData());
        await assertSucceeds(deleteDoc(meterRef));
      });
    });

    describe('As Member', () => {
      let memberDb: any;
      let meterRef: any;

      beforeEach(() => {
        const context = getAuthContext(memberId, 'member');
        memberDb = context.firestore();
        meterRef = doc(memberDb, meterPath);
      });

      it('should allow creating a meter', async () => {
        await assertSucceeds(setDoc(meterRef, createMeterData()));
      });

      it('should allow reading a meter', async () => {
        const managerContext = getAuthContext(managerId, 'manager');
        const managerDb = managerContext.firestore();
        await setDoc(doc(managerDb, meterPath), createMeterData());
        await assertSucceeds(getDoc(meterRef));
      });

      it('should FAIL updating a meter', async () => {
        await setDoc(meterRef, createMeterData());
        await assertFails(updateDoc(meterRef, { meterActive: false }));
      });

      it('should FAIL deleting a meter', async () => {
        await setDoc(meterRef, createMeterData());
        await assertFails(deleteDoc(meterRef));
      });
    });

    describe('As Unauthenticated User', () => {
      let unauthDb: any;
      let meterRef: any;

      beforeEach(() => {
        unauthDb = testEnv.unauthenticatedContext().firestore();
        meterRef = doc(unauthDb, meterPath);
      });

      it('should FAIL creating a meter', async () => {
        await assertFails(setDoc(meterRef, createMeterData()));
      });

      it('should FAIL reading a meter', async () => {
        const managerContext = getAuthContext(managerId, 'manager');
        const managerDb = managerContext.firestore();
        await setDoc(doc(managerDb, meterPath), createMeterData());
        await assertFails(getDoc(meterRef));
      });

      it('should FAIL updating a meter', async () => {
        await assertFails(updateDoc(meterRef, { meterActive: false }));
      });

      it('should FAIL deleting a meter', async () => {
        await assertFails(deleteDoc(meterRef));
      });
    });

    describe('Cross-Organization Access', () => {
      it('should FAIL to create a meter in another org', async () => {
        const context = getAuthContext(managerId, 'manager');
        const invalidData = createMeterData();
        invalidData.organizationId = anotherOrganizationId;
        const invalidRef = doc(context.firestore(), meterPath);
        await assertFails(setDoc(invalidRef, invalidData));
      });

      it('should FAIL to read a meter from another org', async () => {
        const otherContext = getAuthContext(managerId, 'manager', anotherOrganizationId);
        const otherDb = otherContext.firestore();
        const otherRef = doc(otherDb, meterPath);
        await assertFails(getDoc(otherRef));
      });
    });
  });

  describe('Meter Readings Subcollection', () => {
    beforeEach(async () => {
      const adminContext = getAuthContext(adminId, 'admin');
      const adminDb = adminContext.firestore();
      await setDoc(doc(adminDb, meterPath), createMeterData());
    });

    describe('As Manager', () => {
      let managerDb: any;
      let readingRef: any;

      beforeEach(() => {
        const context = getAuthContext(managerId, 'manager');
        managerDb = context.firestore();
        readingRef = doc(managerDb, `${meterPath}/meterReadings/${readingId}`);
      });

      it('should allow creating a reading', async () => {
        await assertSucceeds(setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' }));
      });

      it('should allow reading a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertSucceeds(getDoc(readingRef));
      });

      it('should allow updating a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertSucceeds(updateDoc(readingRef, { consumptionSinceLast: 10 }));
      });

      it('should allow deleting a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertSucceeds(deleteDoc(readingRef));
      });
    });

    describe('As Admin', () => {
      let adminDb: any;
      let readingRef: any;

      beforeEach(() => {
        const context = getAuthContext(adminId, 'admin');
        adminDb = context.firestore();
        readingRef = doc(adminDb, `${meterPath}/meterReadings/${readingId}`);
      });

      it('should allow creating a reading', async () => {
        await assertSucceeds(setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' }));
      });

      it('should allow reading a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertSucceeds(getDoc(readingRef));
      });

      it('should allow updating a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertSucceeds(updateDoc(readingRef, { readingValue: 200 }));
      });

      it('should allow deleting a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertSucceeds(deleteDoc(readingRef));
      });
    });

    describe('As Member', () => {
      let memberDb: any;
      let readingRef: any;

      beforeEach(() => {
        const context = getAuthContext(memberId, 'member');
        memberDb = context.firestore();
        readingRef = doc(memberDb, `${meterPath}/meterReadings/${readingId}`);
      });

      it('should allow creating a reading', async () => {
        await assertSucceeds(setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' }));
      });

      it('should allow reading a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertSucceeds(getDoc(readingRef));
      });

      it('should FAIL updating a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertFails(updateDoc(readingRef, { readingType: 'Auszug' }));
      });

      it('should FAIL deleting a reading', async () => {
        await setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertFails(deleteDoc(readingRef));
      });
    });

    describe('As Unauthenticated User', () => {
      let unauthDb: any;
      let readingRef: any;

      beforeEach(() => {
        unauthDb = testEnv.unauthenticatedContext().firestore();
        readingRef = doc(unauthDb, `${meterPath}/meterReadings/${readingId}`);
      });

      it('should FAIL creating a reading', async () => {
        await assertFails(setDoc(readingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' }));
      });

      it('should FAIL reading a reading', async () => {
        const managerContext = getAuthContext(managerId, 'manager');
        const managerDb = managerContext.firestore();
        const managerReadingRef = doc(managerDb, `${meterPath}/meterReadings/${readingId}`);
        await setDoc(managerReadingRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' });
        await assertFails(getDoc(readingRef));
      });

      it('should FAIL updating a reading', async () => {
        await assertFails(updateDoc(readingRef, { readingValue: 200 }));
      });

      it('should FAIL deleting a reading', async () => {
        await assertFails(deleteDoc(readingRef));
      });
    });

    describe('Cross-Organization Access for Readings', () => {
      it('should FAIL to create a reading for a meter in another org', async () => {
        const context = getAuthContext(managerId, 'manager');
        const db = context.firestore();
        // override meter to another org
        await setDoc(doc(db, meterPath), { ...createMeterData(), organizationId: anotherOrganizationId });
        const invalidRef = doc(db, `${meterPath}/meterReadings/${readingId}`);
        await assertFails(setDoc(invalidRef, { readingDate: Timestamp.now(), readingValue: 100, readingType: 'Einzug' }));
      });

      it('should FAIL to read a reading from another org', async () => {
        const otherContext = getAuthContext(managerId, 'manager', anotherOrganizationId);
        const otherDb = otherContext.firestore();
        const otherRef = doc(otherDb, `${meterPath}/meterReadings/${readingId}`);
        await assertFails(getDoc(otherRef));
      });
    });
  });
});
