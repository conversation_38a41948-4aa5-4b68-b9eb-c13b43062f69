"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateProfileInput = void 0;
const https_1 = require("firebase-functions/v2/https");
/**
 * Validates input data for profile generation
 */
function validateProfileInput(data) {
    if (!data.icpDescription || typeof data.icpDescription !== "string") {
        throw new https_1.HttpsError("invalid-argument", "ICP description is required");
    }
    if (data.productDescription && typeof data.productDescription !== "string") {
        throw new https_1.HttpsError("invalid-argument", "Product description must be a string");
    }
}
exports.validateProfileInput = validateProfileInput;
//# sourceMappingURL=input-validator.js.map