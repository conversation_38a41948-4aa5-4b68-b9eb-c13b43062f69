"use client";

import { useState } from "react";
import { sendEmailVerification } from "firebase/auth";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { XIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/components/providers/auth-provider";

interface EmailVerificationBannerProps {
  className?: string;
}

export function EmailVerificationBanner({
  className,
}: EmailVerificationBannerProps) {
  const { user } = useAuth();
  const [isVisible, setIsVisible] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);

  const currentUser = user;

  if (!currentUser || currentUser.emailVerified || !isVisible) {
    return null;
  }

  async function handleSendVerification() {
    if (!currentUser) return;

    const actionCodeSettings = {
      url: `${window.location.origin}/app/email`,
      handleCodeInApp: true
    };

    try {
      setIsSending(true);
      await sendEmailVerification(currentUser, actionCodeSettings);
      setIsEmailSent(true);
    } catch (error) {
      console.error("Fehler beim Senden der Verifikations-Email:", error);
    } finally {
      setIsSending(false);
    }
  }

  return (
    <Alert
      className={cn(
        "flex flex-col sm:flex-row items-start sm:items-center justify-between border-none bg-violet-100 dark:bg-violet-900 rounded-none mb-4 gap-4 w-full",
        className
      )}
    >
      <AlertDescription className="flex-1 text-foreground">
        Es wurde eine E-Mail an {currentUser.email} gesendet. 
        Bitte bestätigen Sie Ihre E-Mail-Adresse, um alle Funktionen nutzen zu können.
      </AlertDescription>

      <div className="flex items-center gap-2 w-full sm:w-auto">
        <Button
          variant="secondary"
          size="sm"
          onClick={handleSendVerification}
          disabled={isSending || isEmailSent}
          className="flex-1 sm:flex-none"
        >
          {isSending 
            ? "Wird gesendet..." 
            : isEmailSent 
              ? "Bestätigungs-E-Mail wurde gesendet"
              : "Bestätigungs-E-Mail erneut senden"}
        </Button>

        <Button
          variant="secondary"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => setIsVisible(false)}
        >
          <XIcon className="h-4 w-4" />
          <span className="sr-only">Banner ausblenden</span>
        </Button>
      </div>
    </Alert>
  );
}
