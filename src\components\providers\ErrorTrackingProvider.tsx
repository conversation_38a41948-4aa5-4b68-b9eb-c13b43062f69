"use client";

import { useEffect } from "react";
import { errorTrackingService } from "@/lib/error-tracking/errorTrackingService";

interface ErrorTrackingProviderProps {
  children: React.ReactNode;
}

export function ErrorTrackingProvider({ children }: ErrorTrackingProviderProps) {
  // Initialize error tracking on client-side only
  useEffect(() => {
    if (typeof window !== "undefined") {
      errorTrackingService.initialize();
    }
  }, []);

  return <>{children}</>;
} 