/**
 * Represents a user profile from the Firestore 'userProfiles' collection.
 * Contains personal information and user preferences.
 */
export interface UserProfile {
  /** ID of the user */
  id: string;
  /** User's first name */
  firstName: string;
  /** User's last name */
  lastName: string;
  /** Combined display name (typically firstName + lastName) */
  displayName: string;
  /** User's email address */
  email: string;
  /** Optional URL to user's profile picture */
  avatar?: string;
  /** Whether the user has opted in to receive newsletter */
  newsletterOptIn?: boolean;
  /** When the profile was created */
  createdAt?: any; // Firestore timestamp
  /** When the profile was last updated */
  updatedAt?: any; // Firestore timestamp
  /** When the user last logged in */
  lastLogin?: any; // Firestore timestamp
} 