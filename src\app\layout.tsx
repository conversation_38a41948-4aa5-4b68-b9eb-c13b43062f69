import "./globals.css";
import { Inter } from "next/font/google";
import { Toaster } from "sonner";
import { AuthProvider } from "@/components/providers/auth-provider";
import { CookieBanner } from "@/components/cookie-banner";
import { AnalyticsProvider } from '@/components/providers/AnalyticsProvider'
import { ThemeProvider } from "@/components/theme-provider"
import { ErrorTrackingProvider } from "@/components/providers/ErrorTrackingProvider";
import { StructuredData } from "@/components/structured-data";
import type { Metadata } from 'next'
const inter = Inter({ subsets: ["latin"] });

// TODO: Customize metadata for your project
export const metadata: Metadata = {
  title: "VermietOS - Smarte Immobilienverwaltung",
  description: "VermietOS ist Ihre Lösung für die smarte Verwaltung von Immobilien, spezialisiert auf möblierte Vermietung.",
  metadataBase: new URL("https://vermietos.de"), // TODO: Replace with your actual production URL
  keywords: ["Immobilienverwaltung", "Möblierte Vermietung", "Software", "Vermieter", "App"],
  authors: [{ name: "VermietOS" }],
  creator: "VermietOS",
  publisher: "VermietOS",
  alternates: {
    // canonical: 'https://o3domo.com', // Example: Add canonical URL if needed
    //   'en-US': 'https://vermietos.com/en-US', // Example: Add language alternates if needed
  },
  robots: { // Sensible defaults, adjust if needed
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: "VermietOS - Smarte Verwaltung für möblierte Vermietung",
    description: "Vereinfachen Sie die Verwaltung Ihrer möblierten Immobilien mit VermietOS.",
    url: "https://vermietos.de", // TODO: Replace with your actual production URL
    siteName: "VermietOS",
    locale: "de_DE", // Set locale to German
    type: "website",
    images: [],
  },
  twitter: {
    card: "summary_large_image",
    title: "VermietOS - Smarte Verwaltung für möblierte Vermietung",
    description: "Vereinfachen Sie die Verwaltung Ihrer möblierten Immobilien mit VermietOS.",
    siteId: "", // TODO: Add your Twitter Site ID if available
    creator: "@VermietOS", // TODO: Replace with your Twitter handle
    creatorId: "", // TODO: Add your Twitter Creator ID if available
    images: [],
  },
  applicationName: "VermietOS",
  category: "Business", // Or specify a more suitable category
  referrer: "origin-when-cross-origin",
  formatDetection: {
    telephone: false,
    date: false,
    address: false,
    email: false,
    url: false,
  },
  icons: {
    // icon: '/favicon.ico', // TODO: Replace with your favicon path
    // apple: '/apple-touch-icon.png', // TODO: Replace with your apple touch icon path
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="de" suppressHydrationWarning>
      <head>
        <StructuredData type="Organization" />
        <StructuredData type="WebSite" />
      </head>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          <ErrorTrackingProvider>
            <AnalyticsProvider>
              <AuthProvider>
                {children}
              </AuthProvider>
            </AnalyticsProvider>
          </ErrorTrackingProvider>
        </ThemeProvider>
        <CookieBanner />
        <Toaster />
      </body>
    </html>
  );
}
