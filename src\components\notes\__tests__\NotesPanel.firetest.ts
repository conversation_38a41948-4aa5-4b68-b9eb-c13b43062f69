import { 
  initializeTestEnvironment,
  assertSucceeds,
  assertFails,
  RulesTestEnvironment
} from '@firebase/rules-unit-testing';
import {
  doc,
  setDoc,
  getDoc,
  updateDoc,
  arrayUnion,
  arrayRemove,
  Timestamp
} from 'firebase/firestore';
import * as fs from 'fs';

jest.setTimeout(30000);

let testEnv: RulesTestEnvironment;
const projectId = 'o3domo';
const organizationId = 'test-org';
const objectId = 'test-object';
const unitId = 'test-unit';
const managerId = 'manager-user';
const memberId = 'member-user';
const adminId = 'admin-user';

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId,
    firestore: {
      host: 'localhost',
      port: 8080,
      rules: fs.readFileSync('firestore.rules', 'utf8')
    },
    storage: {
      host: 'localhost',
      port: 9199,
      rules: fs.readFileSync('storage.rules', 'utf8')
    }
  });
});

afterEach(async () => {
  await testEnv.clearFirestore();
});

afterAll(async () => {
  await testEnv.cleanup();
});

const getAuthContext = (
  uid: string,
  role: 'admin' | 'manager' | 'member',
  orgId: string = organizationId
) => {
  return testEnv.authenticatedContext(uid, {
    activeOrganizationId: orgId,
    activeRole: role,
    organizations: { [orgId]: role }
  });
};

describe('NotesPanel Security Rules', () => {
  describe('Object notes', () => {
    let managerDb: any;
    let memberDb: any;
    let unauthDb: any;
    const objectPath = `organizations/${organizationId}/objects/${objectId}`;

    beforeEach(async () => {
      const managerCtx = getAuthContext(managerId, 'manager');
      managerDb = managerCtx.firestore();
      memberDb = getAuthContext(memberId, 'member').firestore();
      unauthDb = testEnv.unauthenticatedContext().firestore();
      // initial object creation by manager
      await setDoc(doc(managerDb, objectPath), {
        organizationId,
        createdByUserId: managerId
      });
    });

    it('manager can read object notes', async () => {
      await assertSucceeds(getDoc(doc(managerDb, objectPath)));
    });

    it('member can read object notes', async () => {
      await assertSucceeds(getDoc(doc(memberDb, objectPath)));
    });

    it('unauthenticated cannot read object notes', async () => {
      await assertFails(getDoc(doc(unauthDb, objectPath)));
    });

    it('manager can add a note', async () => {
      const note = {
        id: 'note1',
        content: 'Hello Manager',
        createdByUserId: managerId,
        createdByUserName: 'Manager',
        createdAt: Timestamp.now()
      };
      await assertSucceeds(
        updateDoc(doc(managerDb, objectPath), {
          notesList: arrayUnion(note)
        })
      );
    });

    it('member cannot add a note', async () => {
      const note = {
        id: 'note1',
        content: 'Hello Member',
        createdByUserId: memberId,
        createdByUserName: 'Member',
        createdAt: Timestamp.now()
      };
      await assertFails(
        updateDoc(doc(memberDb, objectPath), {
          notesList: arrayUnion(note)
        })
      );
    });

    it('manager can delete a note', async () => {
      const note = {
        id: 'note1',
        content: 'Delete Me',
        createdByUserId: managerId,
        createdByUserName: 'Manager',
        createdAt: Timestamp.now()
      };
      // add first
      await updateDoc(doc(managerDb, objectPath), { notesList: arrayUnion(note) });
      await assertSucceeds(
        updateDoc(doc(managerDb, objectPath), {
          notesList: arrayRemove(note)
        })
      );
    });

    it('member cannot delete a note', async () => {
      const note = {
        id: 'note1',
        content: 'Delete Me',
        createdByUserId: managerId,
        createdByUserName: 'Manager',
        createdAt: Timestamp.now()
      };
      // add first by manager
      await updateDoc(doc(managerDb, objectPath), { notesList: arrayUnion(note) });
      await assertFails(
        updateDoc(doc(memberDb, objectPath), {
          notesList: arrayRemove(note)
        })
      );
    });

    // Admin tests for object notes
    it('admin can read object notes', async () => {
      const adminDb = getAuthContext(adminId, 'admin').firestore();
      await assertSucceeds(getDoc(doc(adminDb, objectPath)));
    });

    it('admin can add a note', async () => {
      const adminDb = getAuthContext(adminId, 'admin').firestore();
      const note = {
        id: 'note1',
        content: 'Hello Admin',
        createdByUserId: adminId,
        createdByUserName: 'Admin',
        createdAt: Timestamp.now()
      };
      await assertSucceeds(
        updateDoc(doc(adminDb, objectPath), { notesList: arrayUnion(note) })
      );
    });

    it('admin can delete a note', async () => {
      const adminDb = getAuthContext(adminId, 'admin').firestore();
      const note = {
        id: 'note1',
        content: 'Delete Admin',
        createdByUserId: managerId,
        createdByUserName: 'Manager',
        createdAt: Timestamp.now()
      };
      // add first by manager
      await updateDoc(doc(managerDb, objectPath), { notesList: arrayUnion(note) });
      await assertSucceeds(
        updateDoc(doc(adminDb, objectPath), { notesList: arrayRemove(note) })
      );
    });
  });

  describe('Unit notes', () => {
    let managerDb: any;
    let memberDb: any;
    let unauthDb: any;
    const unitPath = `organizations/${organizationId}/objects/${objectId}/units/${unitId}`;

    beforeEach(async () => {
      const managerCtx = getAuthContext(managerId, 'manager');
      managerDb = managerCtx.firestore();
      memberDb = getAuthContext(memberId, 'member').firestore();
      unauthDb = testEnv.unauthenticatedContext().firestore();
      // initial unit creation by manager
      await setDoc(doc(managerDb, unitPath), {
        organizationId,
        objectId,
        createdByUserId: managerId
      });
    });

    it('manager can read unit notes', async () => {
      await assertSucceeds(getDoc(doc(managerDb, unitPath)));
    });

    it('member can read unit notes', async () => {
      await assertSucceeds(getDoc(doc(memberDb, unitPath)));
    });

    it('unauthenticated cannot read unit notes', async () => {
      await assertFails(getDoc(doc(unauthDb, unitPath)));
    });

    it('manager can add unit note', async () => {
      const note = {
        id: 'noteA',
        content: 'Unit Note',
        createdByUserId: managerId,
        createdByUserName: 'Manager',
        createdAt: Timestamp.now()
      };
      await assertSucceeds(
        updateDoc(doc(managerDb, unitPath), {
          notesList: arrayUnion(note)
        })
      );
    });

    it('member cannot add unit note', async () => {
      const note = {
        id: 'noteA',
        content: 'Unit Note',
        createdByUserId: memberId,
        createdByUserName: 'Member',
        createdAt: Timestamp.now()
      };
      await assertFails(
        updateDoc(doc(memberDb, unitPath), {
          notesList: arrayUnion(note)
        })
      );
    });

    it('member cannot delete unit note', async () => {
      const note = {
        id: 'noteA',
        content: 'Unit Note to Delete',
        createdByUserId: managerId,
        createdByUserName: 'Manager',
        createdAt: Timestamp.now()
      };
      // add by manager
      await updateDoc(doc(managerDb, unitPath), { notesList: arrayUnion(note) });
      await assertFails(
        updateDoc(doc(memberDb, unitPath), {
          notesList: arrayRemove(note)
        })
      );
    });

    // Admin tests for unit notes
    it('admin can read unit notes', async () => {
      const adminDb = getAuthContext(adminId, 'admin').firestore();
      await assertSucceeds(getDoc(doc(adminDb, unitPath)));
    });

    it('admin can add unit note', async () => {
      const adminDb = getAuthContext(adminId, 'admin').firestore();
      const note = {
        id: 'noteAdmin',
        content: 'Admin Unit Note',
        createdByUserId: adminId,
        createdByUserName: 'Admin',
        createdAt: Timestamp.now()
      };
      await assertSucceeds(
        updateDoc(doc(adminDb, unitPath), { notesList: arrayUnion(note) })
      );
    });

    it('admin can delete unit note', async () => {
      const adminDb = getAuthContext(adminId, 'admin').firestore();
      const note = {
        id: 'noteAdmin',
        content: 'Delete Admin Unit Note',
        createdByUserId: managerId,
        createdByUserName: 'Manager',
        createdAt: Timestamp.now()
      };
      // add first by manager
      await updateDoc(doc(managerDb, unitPath), { notesList: arrayUnion(note) });
      await assertSucceeds(
        updateDoc(doc(adminDb, unitPath), { notesList: arrayRemove(note) })
      );
    });
  });

  describe('Cross-Organization Access', () => {
    let managerDb: any;
    let anotherOrgManagerDb: any;
    const anotherOrganizationId = 'another-org';
    const objectPath = `organizations/${organizationId}/objects/${objectId}`;
    const unitPath = `organizations/${organizationId}/objects/${objectId}/units/${unitId}`;

    beforeEach(async () => {
      // Setup for the original organization
      const managerCtx = getAuthContext(managerId, 'manager');
      managerDb = managerCtx.firestore();
      await setDoc(doc(managerDb, objectPath), {
        organizationId,
        createdByUserId: managerId
      });
      await setDoc(doc(managerDb, unitPath), {
        organizationId,
        objectId,
        createdByUserId: managerId
      });

      // Setup for the manager from another organization
      const anotherOrgManagerCtx = getAuthContext(
        'another-manager',
        'manager',
        anotherOrganizationId
      );
      anotherOrgManagerDb = anotherOrgManagerCtx.firestore();
    });

    // Object Notes - Cross Org
    it('another org manager cannot read object notes', async () => {
      await assertFails(getDoc(doc(anotherOrgManagerDb, objectPath)));
    });

    it('another org manager cannot add object note', async () => {
      const note = {
        id: 'crossOrgNote',
        content: 'Cross Org Attempt',
        createdByUserId: 'another-manager',
        createdAt: Timestamp.now()
      };
      await assertFails(
        updateDoc(doc(anotherOrgManagerDb, objectPath), {
          notesList: arrayUnion(note)
        })
      );
    });

    it('another org manager cannot delete object note', async () => {
      const note = { id: 'noteToDelete', content: 'Test', createdByUserId: managerId };
      // Add note with the original manager first
      await updateDoc(doc(managerDb, objectPath), { notesList: arrayUnion(note) });
      // Try to delete with the manager from another org
      await assertFails(
        updateDoc(doc(anotherOrgManagerDb, objectPath), {
          notesList: arrayRemove(note)
        })
      );
    });

    // Unit Notes - Cross Org
    it('another org manager cannot read unit notes', async () => {
      await assertFails(getDoc(doc(anotherOrgManagerDb, unitPath)));
    });

    it('another org manager cannot add unit note', async () => {
      const note = {
        id: 'crossOrgUnitNote',
        content: 'Cross Org Unit Attempt',
        createdByUserId: 'another-manager',
        createdAt: Timestamp.now()
      };
      await assertFails(
        updateDoc(doc(anotherOrgManagerDb, unitPath), {
          notesList: arrayUnion(note)
        })
      );
    });

    it('another org manager cannot delete unit note', async () => {
      const note = { id: 'noteUnitToDelete', content: 'Test Unit', createdByUserId: managerId };
       // Add note with the original manager first
      await updateDoc(doc(managerDb, unitPath), { notesList: arrayUnion(note) });
      // Try to delete with the manager from another org
      await assertFails(
        updateDoc(doc(anotherOrgManagerDb, unitPath), {
          notesList: arrayRemove(note)
        })
      );
    });
  });
}); 