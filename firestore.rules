rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Hilfsfunktionen
    function isAuthenticated() {
      return request.auth != null;
    }

    function userOrganizationId() {
      return request.auth.token.activeOrganizationId;
    }

    function isAdmin() {
      return isAuthenticated() && request.auth.token.activeRole == "admin";
    }

    function isManagerOrAdmin() {
      return isAuthenticated() && (request.auth.token.activeRole == "admin" || request.auth.token.activeRole == "manager");
    }

    function isInOrganization(orgId) {
      return isAuthenticated() && ((request.auth.token.organizations != null && orgId in request.auth.token.organizations) || request.auth.token.activeOrganizationId == orgId);
    }

    // NEU: Prüft Mitgliedschaft über Custom Claims (Map-Struktur angenommen)
    function isMemberViaClaims(orgId) {
      return isAuthenticated() && request.auth.token.organizations != null && orgId in request.auth.token.organizations;
    }

    // NEU: Prüft, ob ein Benutzer eine gültige Einladung für eine Organisation hat
    function hasValidInvitation(userEmail, organizationId) {
      return exists(/databases/$(database)/documents/invitations/{invId}) &&
             get(/databases/$(database)/documents/invitations/{invId}).data.email == userEmail &&
             get(/databases/$(database)/documents/invitations/{invId}).data.organizationId == organizationId &&
             get(/databases/$(database)/documents/invitations/{invId}).data.status == "pending";
    }


    

    

    
    // Organization-Regeln
    match /organizations/{orgId} {
      // Lesen: Authentifizert UND Mitglied laut Claims
      allow read: if isAuthenticated() &&
                   (isInOrganization(orgId) ||
                    isMemberViaClaims(orgId));

      // Schreiben: Erlauben für initial setup und vorhandene Admins
      allow create: if isAuthenticated(); // Jeder authentifizierte Benutzer darf Organization erstellen
      allow update: if isAuthenticated() && isInOrganization(orgId) && isManagerOrAdmin();
      allow delete: if isAuthenticated() && isInOrganization(orgId) && isAdmin();
    }

    // userOrganizations Collection Rules
    match /userOrganizations/{userOrgId} {
      // Lesen: Eigene Einträge, Admins/Manager, oder während des initialen Setups
      allow read: if isAuthenticated() &&
                   (request.auth.uid == resource.data.userId ||
                    (isInOrganization(resource.data.organizationId) && isManagerOrAdmin()) ||
                    request.auth.token.activeOrganizationId == null); // Während Setup

      // Erstellen: Nur erlaubt über Cloud Functions oder über eine gültige Einladung
      allow create: if isAuthenticated() &&
                    request.auth.uid == request.resource.data.userId &&
                    hasValidInvitation(request.auth.token.email, request.resource.data.organizationId);

      // Aktualisieren: Nur Admins der Organisation dürfen die Rolle ändern
      allow update: if isAuthenticated() &&
                      isInOrganization(resource.data.organizationId) &&
                      isAdmin() &&
                      request.resource.data.diff(resource.data).affectedKeys().hasOnly(['role']);

      // Löschen: Admins der Organisation ODER der Benutzer selbst
      allow delete: if isAuthenticated() &&
                      ((isInOrganization(resource.data.organizationId) && isAdmin()) ||
                       (request.auth.uid == resource.data.userId));
    }

    // UserProfiles-Regeln
    match /userProfiles/{userId} {
      // Eigenes Profil lesen oder Admins/Manager der Organisation
      allow read: if isAuthenticated() &&
                    (request.auth.uid == userId ||
                    (resource.data.organizationId != null &&
                     isInOrganization(resource.data.organizationId) &&
                     isManagerOrAdmin()));

      // Aktualisieren: Eigene Daten oder Admin-Änderungen
      allow update: if isAuthenticated() &&
                     ((request.auth.uid == userId &&
                       !("role" in request.resource.data) &&
                       !("organizationId" in request.resource.data)) ||
                      (resource.data.organizationId != null &&
                       isInOrganization(resource.data.organizationId) &&
                       isAdmin()));

      // Erstellen: Nur über Cloud Functions
      allow create: if false; // Nur über Cloud Functions
      allow delete: if false; // Nur über Cloud Functions
    }

    // userMetadata Collection
    match /userMetadata/{userId} {
      allow read: if isAuthenticated() && request.auth.uid == userId;
      allow write: if false; // Nur über Cloud Functions
    }

    // Invitation-Regeln
    match /invitations/{invitationId} {
      // Lesen: Ersteller, Empfänger oder Admins/Manager der Organisation
      allow read: if isAuthenticated() &&
                   (request.auth.uid == resource.data.invitedBy ||
                    request.auth.token.email == resource.data.email ||
                    (isInOrganization(resource.data.organizationId) && isManagerOrAdmin()));

      // Erstellen: Admins/Manager für ihre Organisation
      allow create: if isAuthenticated() &&
                     isManagerOrAdmin() &&
                     request.resource.data.organizationId == userOrganizationId() &&
                     request.resource.data.invitedBy == request.auth.uid &&
                     request.resource.data.status == "pending";

      // Aktualisieren: Durch Ersteller oder durch Empfänger bei Annahme
      allow update: if isAuthenticated() &&
                      // Ersteller darf generell aktualisieren
                      (request.auth.uid == resource.data.invitedBy ||
                      // Empfänger darf nur von "pending" auf "accepted" ändern und seine uid als acceptedBy setzen
                      (request.auth.token.email == resource.data.email &&
                       resource.data.status == "pending" &&
                       request.resource.data.status == "accepted" &&
                       request.resource.data.acceptedBy == request.auth.uid &&
                       // Stelle sicher, dass keine anderen Felder geändert werden
                       request.resource.data.diff(resource.data).affectedKeys()
                         .hasOnly(['status', 'acceptedBy', 'acceptedAt'])));

      // Löschen: Nur durch Ersteller oder Admins der Organisation
      allow delete: if isAuthenticated() &&
                     (request.auth.uid == resource.data.invitedBy ||
                      (isInOrganization(resource.data.organizationId) && isAdmin()));
    }

    // Allow authenticated users to create error events
    // <NAME_EMAIL> to read error events
    match /error_events/{errorId} {
      allow create: if request.auth != null;
      allow read: if request.auth != null && request.auth.token.email == '<EMAIL>';
      allow update, delete: if false; // Disallow updates and deletes for everyone
    }


    match /error_events/{document} {
      allow write: if true;  // Nur für Development
  	  allow read: if request.auth != null && request.auth.token.email == "<EMAIL>";
    }
		match /aggregated_stats/{document} {
      allow write: if true;  // Nur für Development
  	  allow read: if request.auth != null && request.auth.token.email == "<EMAIL>";
    }
		match /analytics/{document} {
      allow write: if true;  // Nur für Development
  	  allow read: if request.auth != null && request.auth.token.email == "<EMAIL>";
    }
  }
}


