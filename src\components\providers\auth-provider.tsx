"use client";

import { auth, db, functions } from "@/app/firebase";
import { User } from "firebase/auth";
import { createContext, useContext, useEffect, useState, useCallback, useMemo } from "react";
import { 
  doc, 
  onSnapshot, 
  collection, 
  query, 
  where, 
  getDoc, 
  getDocs, 
  orderBy,
  DocumentData,
  QueryDocumentSnapshot
} from "firebase/firestore";
import { httpsCallable } from "firebase/functions";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface Organization {
  id: string;
  name: string;
  role: string;
  lastAccessed?: any;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  claims: any;
  // Hilfseigenschaften für Rollen
  isAdmin: boolean;
  isManager: boolean;
  isViewer: boolean;
  isAccountant: boolean;
  // Multi-organization support
  userOrganizations: Organization[];
  switchOrganization: (organizationId: string) => Promise<void>;
  activeOrganizationId: string | null;
  activeRole: string | null;
  isLoadingOrganizations: boolean;
  // Neue Hilfsfunktion zum manuellen Token-Refresh
  refreshUserToken: () => Promise<any>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  claims: null,
  isAdmin: false,
  isManager: false,
  isViewer: false,
  isAccountant: false,
  // Multi-organization support
  userOrganizations: [],
  switchOrganization: async () => {},
  activeOrganizationId: null,
  activeRole: null,
  isLoadingOrganizations: false,
  // Neue Hilfsfunktion zum manuellen Token-Refresh
  refreshUserToken: async () => ({}),
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [claims, setClaims] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [userOrganizations, setUserOrganizations] = useState<Organization[]>([]);
  const [isLoadingOrganizations, setIsLoadingOrganizations] = useState(false);
  const router = useRouter();

  // Function to switch active organization
  const switchOrganization = async (organizationId: string) => {
    if (!user) {
      toast.error("Sie müssen angemeldet sein, um die Organisation zu wechseln");
      return;
    }

    try {
      const switchActiveOrg = httpsCallable(functions, 'switchActiveOrganization');
      await switchActiveOrg({ organizationId });
      // The token refresh will be triggered by the userMetadata listener
      toast.success("Organisation gewechselt");
      router.push('/app/dashboard');
    } catch (error: any) {
      console.error('Error switching organization:', error);
      toast.error(`Fehler beim Wechseln der Organisation: ${error.message || 'Unbekannter Fehler'}`);
    }
  };

  // Neue Hilfsfunktion zum manuellen Token-Refresh
  const refreshUserToken = useCallback(async () => {
    if (!auth.currentUser) {
      console.warn('Cannot refresh token: No user is logged in');
      return;
    }
    
    try {
      // Token mit force=true aktualisieren
      const newTokenResult = await auth.currentUser.getIdTokenResult(true);
      setClaims(newTokenResult.claims);
      console.log('Token manually refreshed with claims:', newTokenResult.claims);
      return newTokenResult.claims; // Return the claims for waiting
    } catch (error) {
      console.error('Error manually refreshing token:', error);
      throw error; // Re-throw to allow error handling
    }
  }, []);

  // Load user organizations
  useEffect(() => {
    if (!user) {
      setUserOrganizations([]);
      setIsLoadingOrganizations(false);
      return;
    }

    setIsLoadingOrganizations(true);
    
    const userOrgsRef = collection(db, 'userOrganizations');
    const q = query(
      userOrgsRef, 
      where('userId', '==', user.uid),
      orderBy('lastAccessed', 'desc')
    );
    
    // Set a timeout to prevent getting stuck in loading state
    const timeoutId = setTimeout(() => {
      if (isLoadingOrganizations) {
        setIsLoadingOrganizations(false);
      }
    }, 10000);
    
    const unsubscribeOrgs = onSnapshot(q, async (snapshot) => {
      try {
        // Wait for claims to be ready
        if (!claims) {
          console.log('Waiting for claims to be ready...');
          return;
        }

        const orgPromises = snapshot.docs.map(async (docSnapshot) => {
          try {
            console.log("Processing userOrg doc:", docSnapshot.id, docSnapshot.exists);
            const orgData = docSnapshot.data();

            // Robust Guard: Check if orgData and organizationId exist
            if (!orgData || !orgData.organizationId) {
              console.warn("Skipping userOrganizations doc due to missing data or ID:", docSnapshot.id, orgData);
              return null;
            }

            // Attempt to fetch organization details
            const orgDocRef = doc(db, 'organizations', orgData.organizationId);
            const orgDoc = await getDoc(orgDocRef);

            if (orgDoc.exists()) {
              return {
                id: orgData.organizationId as string,
                name: orgDoc.data().name || 'Unbenannte Organisation',
                role: orgData.role as string || 'viewer',
                lastAccessed: orgData.lastAccessed
              };
            } else {
              console.warn(`Organization document ${orgData.organizationId} not found for userOrg ${docSnapshot.id}`);
              return null;
            }
          } catch (docError: any) {
            console.error(`Error processing individual userOrganization doc ${docSnapshot.id}:`, docError);
            // Log potential permission issues
            if (docError.code === 'permission-denied' || (docError.message && docError.message.includes("permission-denied"))) {
              console.warn(`Potential permission issue fetching organization details for userOrg ${docSnapshot.id}. Claims might be refreshing.`);
            }
            return null;
          }
        });
        
        // Wait for all org promises and filter out nulls
        const resolvedOrgs = (await Promise.all(orgPromises)).filter(org => org !== null) as Organization[];
        setUserOrganizations(resolvedOrgs);
      } catch (snapshotError) {
        console.error('Error processing organizations snapshot:', snapshotError);
      } finally {
        setIsLoadingOrganizations(false);
        clearTimeout(timeoutId);
      }
    }, (queryError: any) => {
      console.error('Error fetching user organizations (onSnapshot query level):', queryError);
      if (queryError.code === 'permission-denied') {
         console.warn("Permission denied on the userOrganizations query itself. Check Firestore rules.");
      }
      setIsLoadingOrganizations(false);
      clearTimeout(timeoutId);
    });
    
    return () => {
      unsubscribeOrgs();
      clearTimeout(timeoutId);
    };
  }, [user, claims]);

  useEffect(() => {
    const currentUser = auth.currentUser;
    if (currentUser) {
      setUser(currentUser);
      // Initiales Token abrufen
      currentUser.getIdTokenResult()
        .then((tokenResult) => {
          setClaims(tokenResult.claims);
          setLoading(false);
        })
        .catch((error) => {
          console.error('Error getting initial token:', error);
          setLoading(false); // Ensure loading is set to false even on error
        });
    }

    // Auth-Status-Listener
    const unsubscribeAuth = auth.onAuthStateChanged(
      async (authUser) => {
        setUser(authUser);
        
        if (authUser) {
          let unsubscribeMeta: (() => void) | null = null;
          
          try {
            // Token-Claims abrufen
            const tokenResult = await authUser.getIdTokenResult();
            setClaims(tokenResult.claims);
            
            // Auf userMetadata hören für Token-Aktualisierungen
            const metadataRef = doc(db, 'userMetadata', authUser.uid);
            unsubscribeMeta = onSnapshot(metadataRef, async () => {
              try {
                // Bei Änderung Token aktualisieren (force refresh)
                const newTokenResult = await authUser.getIdTokenResult(true);
                setClaims(newTokenResult.claims);
              } catch (tokenError) {
                console.error('Error refreshing token:', tokenError);
              }
            }, (error) => {
              console.error('Metadata listener error:', error);
            });
            
          } catch (error) {
            console.error('Error getting token claims:', error);
          } finally {
            // Set loading to false regardless of success or failure
            setLoading(false);
          }
          
          // Return cleanup function directly
          return () => {
            if (unsubscribeMeta) unsubscribeMeta();
          };
        } else {
          // Benutzer ist nicht angemeldet, Claims zurücksetzen
          setClaims(null);
          setLoading(false);
        }
      },
      (error) => {
        console.error('Auth state change error:', error);
        setLoading(false);
      }
    );

    // Ensure loading is set to false after a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (loading) {
        setLoading(false);
      }
    }, 5000);

    // Cleanup Auth-Listener and timeout
    return () => {
      unsubscribeAuth();
      clearTimeout(timeoutId);
    };
  }, [loading]);
  
  // New properties for multi-organization support
  const activeOrganizationId = useMemo(() => claims?.activeOrganizationId || null, [claims]);
  const activeRole = useMemo(() => claims?.activeRole || null, [claims]);
  
  // Role-based helper properties
  const isAdmin = activeRole === 'admin';
  const isManager = activeRole === 'manager' || isAdmin; // Admin hat auch Manager-Rechte
  const isAccountant = activeRole === 'accountant' || isAdmin; // Admin hat auch Accountant-Rechte
  const isViewer = activeRole === 'viewer' || isAdmin || isManager || isAccountant; // Jeder hat mindestens Viewer-Rechte

  return (
    <AuthContext.Provider 
      value={{ 
        user, 
        loading, 
        claims,
        isAdmin,
        isManager,
        isViewer,
        isAccountant,
        userOrganizations,
        switchOrganization,
        activeOrganizationId,
        activeRole,
        isLoadingOrganizations,
        refreshUserToken
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext); 

// Hilfsfunktion für geschützte Routen
export function withRoleCheck(Component: React.ComponentType, requiredRoles?: string[]) {
  return function ProtectedComponent(props: any) {
    const { user, loading, activeRole } = useAuth();
    const [shouldRender, setShouldRender] = useState(false);
    
    useEffect(() => {
      if (!loading) {
        // Wenn keine Rollen angegeben sind, nur prüfen, ob der Benutzer angemeldet ist
        if (!requiredRoles || requiredRoles.length === 0) {
          setShouldRender(!!user);
        } else {
          // Prüfen, ob der Benutzer eine der erforderlichen Rollen hat
          setShouldRender(!!user && !!activeRole && requiredRoles.includes(activeRole));
        }
      }
    }, [user, loading, activeRole]);
    
    if (loading) return null; // Oder einen Loading-Indikator
    if (!shouldRender) return null; // Oder eine Umleitungs-/Zugriffsverweigert-Komponente
    
    return <Component {...props} />;
  };
} 