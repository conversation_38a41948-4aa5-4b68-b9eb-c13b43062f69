---
description: 
globs: *.firetest.ts
alwaysApply: false
---
# Firebase Security Testing Guide

This guide explains the structure and patterns for testing Firebase Security Rules in the codebase.

## Test File Structure

The security tests are organized in `__tests__` directories alongside the components they test. Key test files:

- [src/components/__tests__/DocumentWidget.firetest.ts](mdc:src/components/__tests__/DocumentWidget.firetest.ts) - Tests for document-related rules
- [src/components/__tests__/Tenants.firetest.ts](mdc:src/components/__tests__/Tenants.firetest.ts) - Tests for tenant-related rules

## Test Setup Pattern

Each test file follows this structure:

1. Import required testing utilities and Firebase methods
2. Set up test environment with local emulators
3. Define test data constants and helper functions
4. Create test suites for different user roles

Example setup:
```typescript
let testEnv: RulesTestEnvironment;
const projectId = 'o3domo';

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId,
    firestore: {
      host: 'localhost',
      port: 8080,
      rules: fs.readFileSync('firestore.rules', 'utf8')
    },
    storage: {
      host: 'localhost',
      port: 9199,
      rules: fs.readFileSync('storage.rules', 'utf8')
    }
  });
});
```

## Authentication Context Pattern

Tests use a helper function to create authenticated contexts with different roles:

```typescript
const getAuthContext = (uid: string, role: 'admin' | 'manager' | 'member', orgId: string) => {
  return testEnv.authenticatedContext(uid, {
    activeOrganizationId: orgId,
    activeRole: role,
    organizations: { [orgId]: role }
  });
};
```

## Test Organization

Tests are organized by:
1. User role (Admin, Manager, Member, Unauthenticated)
2. Operation type (Create, Read, Update, Delete)
3. Cross-organization access
4. Invalid data scenarios

Example structure:
```typescript
describe('Firebase Security Rules Tests', () => {
  describe('As Manager', () => {
    // Tests for manager role
  });
  
  describe('As Admin', () => {
    // Tests for admin role
  });
  
  describe('As Member', () => {
    // Tests for member role
  });
});
```

## Testing Both Firestore and Storage

Security tests should cover both Firestore rules ([firestore.rules](mdc:firestore.rules)) and Storage rules ([storage.rules](mdc:storage.rules)) when the feature involves both:

1. Test Firestore document operations first
2. Test corresponding Storage file operations
3. Verify size limits and file type restrictions
4. Test cross-user access patterns

## Best Practices

1. Always test both positive and negative cases
2. Use `assertSucceeds()` and `assertFails()` appropriately
3. Clean up test data in `afterEach()`
4. Test cross-organization access restrictions
5. Verify data validation rules
6. Test file size limits for Storage rules

