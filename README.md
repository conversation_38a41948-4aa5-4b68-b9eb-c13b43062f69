# Next.js Firebase Boilerplate

A modern, production-ready boilerplate for building web applications with Next.js 15, React 19, TypeScript, and Firebase. This template provides a solid foundation with authentication, real-time database, file storage, and a beautiful UI built with Tailwind CSS and Shadcn UI.

## Features

- **Modern Stack**: Next.js 15 with React 19 and TypeScript
- **Authentication**: Complete authentication flow with Firebase Auth
- **Database**: Real-time Firestore database with security rules
- **File Storage**: Firebase Storage integration for file uploads
- **UI Components**: Beautiful, accessible components with Shadcn UI and Tailwind CSS
- **Form Handling**: React Hook Form with Zod validation
- **Theme Support**: Dark/light mode with next-themes
- **Testing**: Jest and React Testing Library setup
- **Type Safety**: Strict TypeScript configuration
- **Development Tools**: ESLint, Prettier, and comprehensive dev tooling

## Technology Stack

- **Frontend**: [Next.js 15](https://nextjs.org/) with [React 19](https://react.dev/) and [TypeScript](https://www.typescriptlang.org/)
- **UI Framework**: [Tailwind CSS](https://tailwindcss.com/) with [Shadcn UI](https://ui.shadcn.com/)
- **Backend**: [Firebase](https://firebase.google.com/) (Firestore, Authentication, Storage, Functions)
- **Hosting**: [Firebase Hosting](https://firebase.google.com/docs/hosting)
- **Charts**: [Recharts](https://recharts.org/)
- **Forms**: [React Hook Form](https://react-hook-form.com/) with [Zod](https://zod.dev/)
- **Notifications**: [Sonner](https://sonner.emilkowal.ski/)

## Project Structure

```
nextjs-firebase-boilerplate/
├── src/
│   ├── app/                # Next.js App Router
│   │   ├── app/            # Protected app routes (after login)
│   │   │   └── dashboard/  # Main dashboard
│   │   ├── auth/           # Authentication pages
│   │   └── ...
│   ├── components/         # Reusable React components
│   │   ├── ui/             # Base UI components (Shadcn)
│   │   ├── auth/           # Authentication components
│   │   └── providers/      # Context providers
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility functions and configurations
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Helper functions
├── functions/              # Firebase Cloud Functions
├── public/                 # Static assets
└── ...
```

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (Version 18 or higher)
- [npm](https://www.npmjs.com/) (Version 9 or higher)
- [Firebase CLI](https://firebase.google.com/docs/cli) for Firebase development

### Installation

1. Clone this repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up Firebase project:
   - Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
   - Enable Authentication, Firestore, and Storage
   - Copy your Firebase configuration

4. Create environment variables:
   Create a `.env.local` file in the root directory:
   ```
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   ```

5. Start development with Firebase emulators:
   ```bash
   npm run dev:emulators
   ```

   Or start just the development server:
   ```bash
   npm run dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Firebase Setup

1. Initialize Firebase in your project:
   ```bash
   firebase init
   ```

2. Configure Firestore rules in `firestore.rules`
3. Configure Storage rules in `storage.rules`
4. Deploy Firebase configuration:
   ```bash
   firebase deploy --only firestore:rules,storage
   ```

## Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run dev:emulators` - Start development with Firebase emulators
- `npm run build` - Build the application for production
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint
- `npm run test:unit` - Run unit tests
- `npm run test:firebase` - Run Firebase-specific tests

## Testing

Run unit tests:
```bash
npm run test:unit
```

Run Firebase tests with emulators:
```bash
npm run test:firebase
```

## Deployment

Deploy to Firebase Hosting:
```bash
npm run build
firebase deploy
```

## Key Features

### Authentication
- Complete authentication flow with Firebase Auth
- Email/password and social provider support
- Protected routes and user session management
- User profile management

### Database
- Real-time Firestore database integration
- Type-safe database operations
- Optimistic updates with SWR
- Comprehensive security rules

### UI Components
- Modern, accessible UI components
- Dark/light theme support
- Responsive design
- Form components with validation

### File Storage
- Firebase Storage integration
- File upload with progress tracking
- Image optimization and resizing
- Secure file access

## Customization

This boilerplate is designed to be easily customizable:

1. **Styling**: Modify the Tailwind configuration and CSS variables
2. **Components**: Add your own components in the `components` directory
3. **Database Schema**: Define your data models in the `types` directory
4. **Authentication**: Customize the auth flow in `components/auth`
5. **Pages**: Add new pages in the `app` directory

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For questions and support, please open an issue in the GitHub repository.
