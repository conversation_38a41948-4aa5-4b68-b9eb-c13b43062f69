"use client";

import { useAuth } from "@/components/providers/auth-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { 
  Building, 
  ChevronsUpDown, 
  Plus,
  Loader2
} from "lucide-react";
import { useState } from "react";
import Link from "next/link";

export function OrganizationSwitcher() {
  const { 
    userOrganizations, 
    activeOrganizationId, 
    switchOrganization, 
    isLoadingOrganizations 
  } = useAuth();
  const { isMobile } = useSidebar();
  const [isOpen, setIsOpen] = useState(false);
  const [isSwitching, setIsSwitching] = useState(false);
  
  // Active organization
  const activeOrg = userOrganizations.find(org => org.id === activeOrganizationId);
  
  const handleSwitchOrg = async (orgId: string) => {
    if (orgId === activeOrganizationId) {
      setIsOpen(false);
      return;
    }
    
    setIsSwitching(true);
    try {
      await switchOrganization(orgId);
      setIsOpen(false);
    } finally {
      setIsSwitching(false);
    }
  };
  
  if (isLoadingOrganizations) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            size="lg"
            disabled
          >
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <Loader2 className="size-4 animate-spin" />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">Lade Organisationen...</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }
  
  if (!activeOrg && userOrganizations.length === 0) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            size="lg"
            disabled
          >
            <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <Building className="size-4" />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">Keine Organisation</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }
  
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild disabled={isSwitching || isLoadingOrganizations}>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground cursor-pointer hover:bg-accent transition-all group"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground group-hover:bg-sidebar-primary/90 transition-colors">
                {isSwitching ? (
                  <Loader2 className="size-4 animate-spin" />
                ) : (
                  <Building className="size-4 group-hover:scale-110 transition-transform" />
                )}
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {activeOrg?.name || userOrganizations[0]?.name || "Organisation"}
                </span>
                <span className="truncate text-xs capitalize">
                  {activeOrg?.role === 'admin' ? 'Administrator' : 
                   activeOrg?.role === 'manager' ? 'Manager' : 
                   activeOrg?.role === 'accountant' ? 'Buchhalter' : 'Betrachter'}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto size-4 group-hover:text-accent-foreground group-hover:rotate-180 transition-all duration-300" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Organisationen
            </DropdownMenuLabel>
            {userOrganizations.map((org, index) => (
              <DropdownMenuItem 
                key={org.id}
                onClick={() => handleSwitchOrg(org.id)}
                className="gap-2 p-2 cursor-pointer hover:bg-accent/50 transition-colors"
              >
                <div className="flex size-6 items-center justify-center rounded-sm bg-primary/10 hover:bg-primary/20 transition-colors">
                  <Building className="size-4 shrink-0" />
                </div>
                <div className="flex flex-col flex-1">
                  <span>{org.name}</span>
                  <span className="text-xs text-muted-foreground capitalize">
                    {org.role === 'admin' ? 'Administrator' : 
                     org.role === 'manager' ? 'Manager' : 
                     org.role === 'accountant' ? 'Buchhalter' : 'Betrachter'}
                  </span>
                </div>
              </DropdownMenuItem>
            ))}
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem asChild className="gap-2 p-2">
              <Link href="/app/organizations/create" className="cursor-pointer hover:bg-accent/50 transition-all w-full">
                <div className="flex size-6 items-center justify-center rounded-md bg-primary/10 hover:bg-primary/20 transition-colors">
                  <Plus className="size-4 hover:scale-110 transition-transform" />
                </div>
                <div className="font-medium text-muted-foreground">Neue Organisation erstellen</div>
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
} 