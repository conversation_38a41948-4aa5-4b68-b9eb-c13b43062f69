import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formatiert ein Datum im deutschen Format mit Wochentag, Datum und Uhrzeit
 * @param date Das zu formatierende Datum
 * @returns Formatierter Datumsstring
 * @throws {RangeError} Wenn das Datum ungültig ist
 * @throws {TypeError} Wenn der Parameter kein Date-Objekt ist
 */
export function formatDate(date: Date): string {
  // Validierungsprüfungen
  if (!(date instanceof Date)) {
    throw new TypeError("Das übergebene Argument muss ein Date-Objekt sein");
  }
  
  if (isNaN(date.getTime())) {
    throw new RangeError("Das übergebene Datum ist ungültig");
  }
  
  return new Intl.DateTimeFormat("de", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
}

/**
 * Formatiert einen Betrag als Währung im deutschen Format
 * @param amount Der zu formatierende Betrag
 * @param currency Die Währung (Standard: EUR)
 * @returns Formatierter Währungsstring
 */
export function formatCurrency(amount: number, currency: string = "EUR"): string {
  return new Intl.NumberFormat("de-DE", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
}