---
description: 
globs: *.firetest.ts
alwaysApply: false
---
# Firebase Security Rules Structure

This guide explains the organization and patterns of Firebase Security Rules in the codebase.

## Rule Files

The security rules are defined in two main files:

- [firestore.rules](mdc:firestore.rules) - Rules for Firestore database access
- [storage.rules](mdc:storage.rules) - Rules for Cloud Storage access

## Common Helper Functions

Both rule sets use similar helper functions to check authentication and roles:

```javascript
function isAuthenticated() {
  return request.auth != null;
}

function isManagerOrAdmin() {
  return isAuthenticated() && 
    (request.auth.token.activeRole == "admin" || 
     request.auth.token.activeRole == "manager");
}

function isInOrganization(orgId) {
  return isAuthenticated() && 
    (request.auth.token.organizations != null && 
     orgId in request.auth.token.organizations ||
     request.auth.token.activeOrganizationId == orgId);
}
```

## Organization-Based Access Control

The rules follow an organization-based access control pattern:

1. Resources are nested under organizations
2. Users must be members of an organization to access its resources
3. Different roles (admin, manager, member) have different permissions
4. Cross-organization access is explicitly prevented

Example pattern:
```javascript
match /organizations/{orgId}/tenants/{tenantId} {
  allow read: if isInOrganization(orgId);
  allow write: if isInOrganization(orgId) && isManagerOrAdmin();
}
```

## Storage Rules Pattern

Storage rules mirror Firestore's organization structure:

```javascript
match /organizations/{orgId}/{type}/{itemId}/{fileId} {
  allow read: if isInOrganization(orgId);
  allow create: if isInOrganization(orgId) && 
                   isManagerOrAdmin() &&
                   request.resource.size < 25 * 1024 * 1024;
}
```

## Testing Rules

Rules should be tested using the Firebase Rules Unit Testing framework. See test examples in:

- [src/components/__tests__/Tenants.firetest.ts](mdc:src/components/__tests__/Tenants.firetest.ts)
- [src/components/__tests__/DocumentWidget.firetest.ts](mdc:src/components/__tests__/DocumentWidget.firetest.ts)

## Best Practices

1. Always use helper functions for common checks
2. Validate resource paths match organization context
3. Enforce size limits on file uploads
4. Prevent cross-organization access
5. Test all rules thoroughly
6. Document complex rule logic with comments

