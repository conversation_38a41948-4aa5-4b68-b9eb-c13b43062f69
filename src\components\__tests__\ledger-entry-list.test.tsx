import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LedgerEntryList } from '../ledger-entry-list';
import { useAuth } from '@/components/providers/auth-provider';
import { LedgerService } from '@/services/ledger-service';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/components/providers/auth-provider');
jest.mock('@/services/ledger-service');
jest.mock('sonner');

// Mock data
const mockEntries = [
  {
    id: 'entry1',
    category: 'rent',
    period: '2023-01',
    expectedDate: new Date('2023-01-15'),
    amountExpected: 1000,
    status: 'expected',
    paymentReference: 'REF123',
    createdAt: new Date(),
    updatedAt: new Date(),
    organizationId: 'org123',
    contractId: 'contract123',
    propertyId: 'property123',
    unitId: 'unit123',
    counterpartyId: 'tenant123',
    kind: 'income',
    currency: 'EUR',
    createdFrom: 'system',
    createdByUserId: 'user123',
  },
  {
    id: 'entry2',
    category: 'deposit-in',
    expectedDate: new Date('2023-01-01'),
    amountExpected: 2000,
    status: 'paid',
    receivedDate: new Date('2023-01-05'),
    amountReceived: 2000,
    paymentReference: 'DEP123',
    createdAt: new Date(),
    updatedAt: new Date(),
    organizationId: 'org123',
    contractId: 'contract123',
    propertyId: 'property123',
    unitId: 'unit123',
    counterpartyId: 'tenant123',
    kind: 'income',
    currency: 'EUR',
    createdFrom: 'system',
    createdByUserId: 'user123',
  },
];

describe('LedgerEntryList', () => {
  beforeEach(() => {
    // Mock useAuth hook
    (useAuth as jest.Mock).mockReturnValue({
      activeOrganizationId: 'org123',
    });

    // Mock LedgerService
    (LedgerService.getEntries as jest.Mock).mockResolvedValue(mockEntries);
    (LedgerService.deleteEntry as jest.Mock).mockResolvedValue(undefined);

    // Mock toast
    (toast.success as jest.Mock).mockImplementation(() => {});
    (toast.error as jest.Mock).mockImplementation(() => {});
    (toast.info as jest.Mock).mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders ledger entries correctly', async () => {
    render(<LedgerEntryList />);

    // Wait for entries to load
    await waitFor(() => {
      expect(screen.getByText('Miete')).toBeInTheDocument();
      expect(screen.getByText('Kaution (Eingang)')).toBeInTheDocument();
    });

    // Check if amounts are displayed correctly
    expect(screen.getByText('1.000,00 €')).toBeInTheDocument();
    expect(screen.getByText('2.000,00 €')).toBeInTheDocument();

    // Check if status badges are displayed
    expect(screen.getByText('Offen')).toBeInTheDocument();
    expect(screen.getByText('Bezahlt')).toBeInTheDocument();
  });

  it('deletes an entry when confirmed', async () => {
    const user = userEvent.setup();
    render(<LedgerEntryList />);

    // Wait for entries to load
    await waitFor(() => {
      expect(screen.getByText('Miete')).toBeInTheDocument();
    });

    // Open the dropdown menu for the first entry
    const menuButtons = screen.getAllByRole('button', { name: 'Menü öffnen' });
    await user.click(menuButtons[0]);

    // Click the delete option
    const deleteOption = screen.getByText('Löschen');
    await user.click(deleteOption);

    // Confirm deletion in the dialog
    const confirmButton = screen.getByRole('button', { name: 'Löschen' });
    await user.click(confirmButton);

    // Check if deleteEntry was called with correct parameters
    await waitFor(() => {
      expect(LedgerService.deleteEntry).toHaveBeenCalledWith('org123', 'entry1');
      expect(toast.success).toHaveBeenCalledWith('Eintrag erfolgreich gelöscht');
    });
  });

  it('calls getEntries with correct filters when rendered with category prop', async () => {
    // Render with category prop
    render(<LedgerEntryList category="rent" />);

    // Wait for entries to load
    await waitFor(() => {
      // Check if getEntries was called with correct filter
      expect(LedgerService.getEntries).toHaveBeenCalledWith(
        'org123',
        expect.objectContaining({ category: 'rent' })
      );
    });
  });
});
