rules_version = '2';

service firebase.storage {
  // Helper Functions
  function isAuthenticated() {
    return request.auth != null;
  }

  function isInOrganization(orgId) {
    return isAuthenticated() && 
           request.auth.token.activeOrganizationId == orgId;
  }

  function isAdmin() {
    return isAuthenticated() && request.auth.token.admin == true;
  }

  match /b/{bucket}/o {
    // Organization documents and files
    match /organizations/{orgId}/{allPaths=**} {
      // Read: Allow authenticated members of the organization
      allow read: if isInOrganization(orgId);

      // Create: Allow uploads for organization members (max 25MB)
      allow create: if isInOrganization(orgId) &&
                       request.resource.size < 25 * 1024 * 1024;

      // Update: Generally disallowed for immutable storage
      allow update: if false;

      // Delete: Allow deletions for authenticated organization members
      allow delete: if isInOrganization(orgId);
    }

    // User profile images and documents
    match /users/{userId}/{allPaths=**} {
      // Read/Write: Allow only the user themselves or admins
      allow read, write: if isAuthenticated() && 
                           (request.auth.uid == userId || isAdmin());
    }

    // Default deny all other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
