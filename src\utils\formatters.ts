/**
 * Format a date to a German-style string (DD.MM.YYYY)
 * @param date Date to format
 * @returns Formatted date string
 */
export function formatDateToGermanString(date: Date | string | number): string {
  if (!date) return "";

  try {
    const dateObj = date instanceof Date ? date : new Date(date);

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) {
      console.warn("Invalid date detected in formatDateToGermanString:", date);
      return "Ungültiges Datum";
    }

    return dateObj.toLocaleDateString("de-DE", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Ungültiges Datum";
  }
}

/**
 * Format a currency value to a German-style string (e.g., 1.234,56 €)
 * @param amount Amount to format
 * @param currency Currency code (default: EUR)
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = "EUR"): string {
  if (amount === undefined || amount === null) return "";

  return new Intl.NumberFormat("de-DE", {
    style: "currency",
    currency,
  }).format(amount);
}

/**
 * Format a number to a German-style string (e.g., 1.234,56)
 * @param value Number to format
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted number string
 */
export function formatNumber(value: number, decimals: number = 2): string {
  if (value === undefined || value === null) return "";

  return new Intl.NumberFormat("de-DE", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

/**
 * Format a percentage value to a German-style string (e.g., 12,34 %)
 * @param value Percentage value (e.g., 0.1234 for 12.34%)
 * @param decimals Number of decimal places (default: 2)
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number, decimals: number = 2): string {
  if (value === undefined || value === null) return "";

  return new Intl.NumberFormat("de-DE", {
    style: "percent",
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}
