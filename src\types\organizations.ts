import { Address } from "./index";

/**
 * Default contract values for a specific unit type
 */
export interface UnitTypeDefaults {
  /** Default cleaning fee for this unit type */
  cleaningCosts?: number | null;
  /** Default security deposit for this unit type */
  deposit?: number | null;
  /** Default notice period in months for this unit type */
  noticePeriod?: number | null;
  /** Default notice period unit for this unit type */
  noticePeriodUnit?: "days" | "weeks" | "months" | "years";
}

/**
 * Default contract values for different unit types
 */
export interface ContractDefaults {
  /** Default values for apartment units */
  Wohnung?: UnitTypeDefaults;
  /** Default values for room units */
  Zimmer?: UnitTypeDefaults;
  /** Default values for other unit types */
  Sonstige?: UnitTypeDefaults;
}

/**
 * Represents an organization from the Firestore 'organizations' collection.
 * Contains information about a company or group.
 */
export interface Organization {
  /** ID of the organization */
  id: string;
  /** Name of the organization */
  name: string;
  /** Primary contact email for the organization */
  contactEmail?: string;
  /** Organization description */
  description?: string;
  /** Organization website URL */
  website?: string;
  /** Organization phone number */
  phone?: string;

  /** Address of the organization */
  address?: Address;

  /** Payment details */
  /** IBAN (International Bank Account Number) */
  iban?: string;
  /** Account holder name */
  accountHolder?: string;
  /** BIC (Bank Identifier Code) */
  bic?: string;
  /** Bank name */
  bankName?: string;

  /** Default contract values for different unit types */
  contractDefaults?: ContractDefaults;

  /** When the organization was created (if tracked) */
  createdAt?: any; // Firestore timestamp
  /** When the organization was last updated (if tracked) */
  updatedAt?: any; // Firestore timestamp
}
