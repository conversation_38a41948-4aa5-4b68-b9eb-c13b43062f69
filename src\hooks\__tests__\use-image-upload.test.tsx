import { renderHook, act } from "@testing-library/react";
import { useImageUpload } from "../use-image-upload";

describe("useImageUpload", () => {
  // Mock URL.createObjectURL and URL.revokeObjectURL
  const mockCreateObjectURL = jest.fn();
  const mockRevokeObjectURL = jest.fn();
  global.URL.createObjectURL = mockCreateObjectURL;
  global.URL.revokeObjectURL = mockRevokeObjectURL;

  beforeEach(() => {
    // Reset mocks before each test
    mockCreateObjectURL.mockClear();
    mockRevokeObjectURL.mockClear();
  });

  it("should initialize with null values", () => {
    const { result } = renderHook(() => useImageUpload());

    expect(result.current.previewUrl).toBeNull();
    expect(result.current.fileName).toBeNull();
    expect(result.current.fileInputRef.current).toBeNull(); // Initial ref value
  });

  it("should update previewUrl and fileName when a file is selected", () => {
    const { result } = renderHook(() => useImageUpload());
    const mockFile = new File(["dummy content"], "test.png", { type: "image/png" });
    const mockEvent = {
      target: { files: [mockFile] },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    const mockUrl = "blob:http://localhost/mock-url";
    mockCreateObjectURL.mockReturnValue(mockUrl);

    act(() => {
      result.current.handleFileChange(mockEvent);
    });

    expect(result.current.previewUrl).toBe(mockUrl);
    expect(result.current.fileName).toBe("test.png");
    expect(mockCreateObjectURL).toHaveBeenCalledWith(mockFile);
  });

  it("should call onUpload callback when a file is selected", () => {
    const handleUpload = jest.fn();
    const { result } = renderHook(() => useImageUpload({ onUpload: handleUpload }));
    const mockFile = new File(["dummy content"], "test.png", { type: "image/png" });
    const mockEvent = {
      target: { files: [mockFile] },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    const mockUrl = "blob:http://localhost/mock-url";
    mockCreateObjectURL.mockReturnValue(mockUrl);

    act(() => {
      result.current.handleFileChange(mockEvent);
    });

    expect(handleUpload).toHaveBeenCalledWith(mockUrl);
  });

  it("should reset state and revoke URL on handleRemove", () => {
    const { result } = renderHook(() => useImageUpload());
    const mockFile = new File(["dummy content"], "test.png", { type: "image/png" });
    const mockEvent = {
      target: { files: [mockFile] },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    const mockUrl = "blob:http://localhost/mock-url";
    mockCreateObjectURL.mockReturnValue(mockUrl);

    // Simulate file selection
    act(() => {
      result.current.handleFileChange(mockEvent);
    });

    expect(result.current.previewUrl).toBe(mockUrl);

    // Simulate remove
    act(() => {
      result.current.handleRemove();
    });

    expect(result.current.previewUrl).toBeNull();
    expect(result.current.fileName).toBeNull();
    expect(mockRevokeObjectURL).toHaveBeenCalledWith(mockUrl);
  });

  it("should call click on file input ref when handleThumbnailClick is called", () => {
    const { result } = renderHook(() => useImageUpload());
    const mockInputRef = {
      current: {
        click: jest.fn(),
        value: "",
      } as unknown as HTMLInputElement,
    };
    // Assign the mock ref to the hook's ref
    (result.current.fileInputRef as React.MutableRefObject<HTMLInputElement | null>).current = mockInputRef.current;


    act(() => {
      result.current.handleThumbnailClick();
    });

    expect(mockInputRef.current.click).toHaveBeenCalledTimes(1);
  });

  it("should revoke object URL on unmount", () => {
    const { result, unmount } = renderHook(() => useImageUpload());
    const mockFile = new File(["dummy content"], "test.png", { type: "image/png" });
    const mockEvent = {
      target: { files: [mockFile] },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    const mockUrl = "blob:http://localhost/mock-url-unmount";
    mockCreateObjectURL.mockReturnValue(mockUrl);

    act(() => {
      result.current.handleFileChange(mockEvent);
    });

    expect(result.current.previewUrl).toBe(mockUrl);
    mockRevokeObjectURL.mockClear(); // Clear calls from handleRemove if any

    unmount();

    expect(mockRevokeObjectURL).toHaveBeenCalledWith(mockUrl);
  });

  it("should not update state when no file is selected", () => {
    const { result } = renderHook(() => useImageUpload());
    const mockEvent = {
      target: { files: [] },
    } as unknown as React.ChangeEvent<HTMLInputElement>;

    act(() => {
      result.current.handleFileChange(mockEvent);
    });

    expect(result.current.previewUrl).toBeNull();
    expect(result.current.fileName).toBeNull();
    expect(mockCreateObjectURL).not.toHaveBeenCalled();
  });

  it("should handle selecting a new file after already having one", () => {
    const { result } = renderHook(() => useImageUpload());
    
    // First file selection
    const firstFile = new File(["first content"], "first.png", { type: "image/png" });
    const firstEvent = {
      target: { files: [firstFile] },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    const firstUrl = "blob:http://localhost/first-url";
    mockCreateObjectURL.mockReturnValue(firstUrl);

    act(() => {
      result.current.handleFileChange(firstEvent);
    });

    expect(result.current.previewUrl).toBe(firstUrl);
    expect(result.current.fileName).toBe("first.png");
    
    // Second file selection
    const secondFile = new File(["second content"], "second.png", { type: "image/png" });
    const secondEvent = {
      target: { files: [secondFile] },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    const secondUrl = "blob:http://localhost/second-url";
    mockCreateObjectURL.mockReturnValue(secondUrl);
    
    act(() => {
      result.current.handleFileChange(secondEvent);
    });

    // Should update to the new file
    expect(result.current.previewUrl).toBe(secondUrl);
    expect(result.current.fileName).toBe("second.png");
    expect(mockCreateObjectURL).toHaveBeenCalledWith(secondFile);
    
    // Original URL should be revoked when new one is created
    // This is implementation-dependent - if the hook doesn't automatically
    // revoke previous URLs when selecting a new file, this expectation
    // might need adjustment
    // expect(mockRevokeObjectURL).toHaveBeenCalledWith(firstUrl);
  });
});
