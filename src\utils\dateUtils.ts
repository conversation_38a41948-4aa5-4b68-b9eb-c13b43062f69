import { format, parse, isValid, startOfDay } from "date-fns";
import { de } from "date-fns/locale";

const GERMAN_DATE_FORMAT = 'dd.MM.yyyy';

/**
 * Validates if a string is a valid date in German format (DD.MM.YYYY),
 * is in the past (or today), and the year is reasonable (>= 1900).
 * @param dateStr The date string to validate.
 * @returns True if the date string is valid, false otherwise.
 */
export const isValidGermanDateString = (dateStr: string | undefined | null): boolean => {
  if (!dateStr) return false;

  // Check format with regex
  const dateRegex = /^(\d{2})\.(\d{2})\.(\d{4})$/;
  if (!dateRegex.test(dateStr)) return false;

  // Parse the date string using date-fns
  const parsedDate = parse(dateStr, GERMAN_DATE_FORMAT, new Date());

  // Check if parsing was successful and the formatted date matches the input (handles invalid dates like 31.02.2023)
  if (!isValid(parsedDate) || format(parsedDate, GERMAN_DATE_FORMAT) !== dateStr) {
      return false;
  }

  // Additional checks: year range and date in the past
  const year = parsedDate.getFullYear();
  const currentYear = new Date().getFullYear();

  // Use startOfDay to compare dates without time component
  return year >= 1900 && year <= currentYear && startOfDay(parsedDate) <= startOfDay(new Date());
};

/**
 * Parses a date string in German format (DD.MM.YYYY) into a Date object.
 * Assumes the string is already validated.
 * @param dateStr The date string to parse.
 * @returns The parsed Date object, or null if parsing fails (should not happen with validated string).
 */
export const parseGermanDateString = (dateStr: string): Date | null => {
  const parsedDate = parse(dateStr, GERMAN_DATE_FORMAT, new Date());
  return isValid(parsedDate) ? parsedDate : null;
};

/**
 * Formats a Date object into a German date string (DD.MM.YYYY).
 * @param date The Date object to format.
 * @returns The formatted date string, or an empty string if the date is invalid or null/undefined.
 */
export const formatDateToGermanString = (date: Date | undefined | null): string => {
  if (!date || !isValid(date)) {
    return "";
  }
  return format(date, GERMAN_DATE_FORMAT, { locale: de });
}; 

/**
 * Formats a Date object into a long German date string including the weekday.
 * Example: "Montag, 01.Januar"
 * @param date The Date object to format.
 * @returns The formatted date string.
 */
export function formatDate(date: Date) {
  return format(date, "EEEE, dd.MMMM", { locale: de });
}

/**
 * Returns a time-dependent greeting based on the provided locale.
 * Supports German ("de") and defaults to English for other locales.
 * @param locale The locale string ('de' for German, others for English).
 * @returns A greeting string like "Guten Morgen,", "Good Afternoon,", etc.
 */
export function getGreeting(locale: string): string {
  const hour = new Date().getHours();
  if (locale === "de") {
    if (hour >= 5 && hour < 12) return "Guten Morgen,";
    if (hour >= 12 && hour < 18) return "Guten Tag,";
    if (hour >= 18 && hour < 22) return "Guten Abend,";
    return "Gute Nacht,";
  } else {
    if (hour >= 5 && hour < 12) return "Good Morning,";
    if (hour >= 12 && hour < 18) return "Good Afternoon,";
    if (hour >= 18 && hour < 22) return "Good Evening,";
    return "Good Night,";
  }
}