// Define our auth token interface for type assertions
export interface AuthToken {
  email?: string;
  activeRole?: string;
  activeOrganizationId?: string;
  organizationId?: string;
  role?: string;
}

// Define our auth interface for type assertions
export interface Auth {
  uid: string;
  token: AuthToken;
}

// Data interfaces for Firebase Functions v1
export interface SetInitialClaimsData {
  organizationId: string;
}

export interface ChangeUserRoleData {
  targetUid: string;
  newRole: string;
}

export interface CreateUserInvitationData {
  email: string;
  role?: string;
}

export interface AcceptInvitationData {
  invitationId: string;
}

export interface SyncUserRoleClaimsData {
  userId?: string;
  organizationId?: string;
}

export interface SwitchActiveOrganizationData {
  organizationId: string;
}

export interface UpdateOrganizationData {
  name?: string;
  contactEmail?: string;
  iban?: string;
  accountHolder?: string;
  bic?: string;
  bankName?: string;
}

export interface DeleteOrganizationData {
  organizationId: string;
}

export interface CreateOrganizationData {
  name: string;
  contactEmail?: string;
  iban?: string;
  accountHolder?: string;
  bic?: string;
  bankName?: string;
  address?: {
    street?: string;
    houseNumber?: string;
    zipCode?: string;
    city?: string;
    country?: string;
    additionalInfo?: string;
  };
}
