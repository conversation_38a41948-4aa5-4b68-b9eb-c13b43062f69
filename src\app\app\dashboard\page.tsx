import StoryGenerator from "@/components/story-generator";
import { FileText } from "lucide-react";

export default async function DashboardPage() {
  return (
    <div className="container mx-auto px-4 md:px-6 pb-12 pt-8">
      {/* Header */}
      <div className="mb-10 max-w-2xl">
        <h1 className="text-4xl font-bold tracking-tight mb-3">Story-Generator</h1>
        <p className="text-muted-foreground text-lg">
          Verwandle Rohfeedback in strukturierte User Stories mit klaren Prioritäten und Umsetzungsempfehlungen
        </p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-8">
        {/* User Story Generator */}
        <div className="max-w-8xl">
          <StoryGenerator />
        </div>
      </div>
    </div>
  );
}
