import { HttpsError } from "firebase-functions/v2/https";
import { logger } from "firebase-functions/v2";
import * as admin from "firebase-admin";

/**
 * Extracts ID token from Authorization header
 */
export function extractIdToken(authHeader?: string): string | null {
  return authHeader && authHeader.startsWith('Bearer ') 
    ? authHeader.split('Bearer ')[1] 
    : null;
}

/**
 * Validates user authentication token
 */
export async function validateAuth(idToken: string): Promise<admin.auth.DecodedIdToken> {
  if (!idToken) {
    throw new HttpsError("unauthenticated", "User must be authenticated");
  }

  try {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    if (!decodedToken.email_verified) {
      throw new HttpsError("unauthenticated", "User must verify email first");
    }
    
    return decodedToken;
  } catch (error) {
    logger.warn("Authentication failed", { error: String(error) });
    throw new HttpsError("unauthenticated", "Invalid authentication token");
  }
} 