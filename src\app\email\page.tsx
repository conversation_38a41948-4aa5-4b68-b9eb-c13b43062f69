"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { auth } from "@/app/firebase";
import { applyActionCode, confirmPasswordReset } from "firebase/auth";
import {
  <PERSON>,
  CardHeader,
  CardContent,
} from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ActionState {
  status: "verifying" | "success" | "error" | "waiting";
  error?: string;
}

export default function EmailActionPage() {
  const searchParams = useSearchParams();
  const [state, setState] = useState<ActionState>({ status: "verifying" });
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  useEffect(() => {
    async function handleEmailAction() {
      const mode = searchParams.get("mode");
      const oobCode = searchParams.get("oobCode");
      
      if (!oobCode) {
        setState({ status: "error", error: "Kein Aktionscode gefunden." });
        return;
      }

      try {
        if (mode === "verifyEmail") {
          await applyActionCode(auth, oobCode);
          if (auth.currentUser) {
            await auth.currentUser.reload();
            await auth.currentUser.getIdToken(true);
          }
          setState({ status: "success" });
        } else if (mode === "resetPassword") {
          // Für Password Reset zeigen wir das Formular an
          setState({ status: "waiting" });
        } else {
          setState({ status: "error", error: "Ungültige Aktion." });
        }
      } catch (error: any) {
        if (error?.code === "auth/invalid-action-code" && auth.currentUser?.emailVerified) {
          setState({ status: "success" });
        } else {
          setState({ 
            status: "error", 
            error: "Der Aktionscode ist ungültig oder abgelaufen." 
          });
          console.error("Fehler bei der Email-Aktion:", error);
        }
      }
    }

    handleEmailAction();
  }, [searchParams]);

  async function handlePasswordReset(e: React.FormEvent) {
    e.preventDefault();
    const oobCode = searchParams.get("oobCode");
    
    if (!oobCode) return;
    
    if (newPassword !== confirmPassword) {
      setState({ 
        status: "error", 
        error: "Die Passwörter stimmen nicht überein." 
      });
      return;
    }

    try {
      setState({ status: "verifying" });
      await confirmPasswordReset(auth, oobCode, newPassword);
      setState({ status: "success" });
    } catch (error: any) {
      setState({ 
        status: "error", 
        error: "Fehler beim Zurücksetzen des Passworts." 
      });
      console.error("Fehler beim Password Reset:", error);
    }
  }

  return (
    <div className="container max-w-md mx-auto mt-6">
      <Card className="border-none shadow-none">
        <CardHeader>
          <h1 className="text-2xl font-bold text-center">
            {searchParams.get("mode") === "resetPassword" 
              ? "Passwort zurücksetzen"
              : "Email-Verifizierung"}
          </h1>
        </CardHeader>
        
        <CardContent className="text-center">
          {state.status === "verifying" && (
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-violet-600" />
              <p>Bitte warten...</p>
            </div>
          )}
          
          {state.status === "waiting" && (
            <form onSubmit={handlePasswordReset} className="space-y-4">
              <div className="space-y-2">
                <Input
                  type="password"
                  placeholder="Neues Passwort"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  minLength={6}
                />
                <Input
                  type="password"
                  placeholder="Passwort bestätigen"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  minLength={6}
                />
                <p className="text-sm text-muted-foreground mt-2">
                  Das Passwort muss mindestens 6 Zeichen lang sein und sollte Buchstaben, 
                  Zahlen und Sonderzeichen enthalten.
                </p>
              </div>
              <Button type="submit" className="w-full">
                Passwort ändern
              </Button>
            </form>
          )}
          
          {state.status === "success" && (
            <div className="space-y-4">
              <p className="text-green-600">
                {searchParams.get("mode") === "resetPassword"
                  ? "Dein Passwort wurde erfolgreich zurückgesetzt!"
                  : "Deine Email-Adresse wurde erfolgreich verifiziert!"}
              </p>
              <p>Du kannst diese Seite nun schließen.</p>
            </div>
          )}
          
          {state.status === "error" && (
            <div className="space-y-4">
              <p className="text-red-600">{state.error}</p>
              <p>
                Bitte fordere einen neuen Link an oder kontaktiere
                den Support.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
