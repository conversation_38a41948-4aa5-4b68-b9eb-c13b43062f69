"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerWithRangeProps {
  date: DateRange | undefined;
  onDateChange: (date: DateRange | undefined) => void;
  className?: string;
}

export function DatePickerWithRange({
  date,
  onDateChange,
  className,
}: DatePickerWithRangeProps) {
  const handleSelect = (range: DateRange | undefined) => {
    if (!range) {
      onDateChange(undefined);
      return;
    }

    // Setze "from" auf 00:00:00 und "to" auf 23:59:59
    const normalizedRange: DateRange = {
      from: range.from ? new Date(range.from.setHours(0, 0, 0, 0)) : undefined,
      to: range.to ? new Date(range.to.setHours(23, 59, 59, 999)) : undefined,
    };

    onDateChange(normalizedRange);
  };

  const [isMounted, setIsMounted] = React.useState(false);
  const [numberOfMonths, setNumberOfMonths] = React.useState(1);

  // Verwende useEffect, um auf window.innerWidth zuzugreifen, nachdem die Komponente gemounted ist
  React.useEffect(() => {
    setIsMounted(true);
    const handleResize = () => {
      setNumberOfMonths(window.innerWidth >= 768 ? 2 : 1);
    };

    // Initial setzen
    handleResize();

    // Event-Listener für Resize-Events
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "justify-start text-left font-normal w-full",
            !date && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date?.from ? (
            date.to ? (
              <>
                {format(date.from, "dd.MM.yyyy")} -{" "}
                {format(date.to, "dd.MM.yyyy")}
              </>
            ) : (
              format(date.from, "dd.MM.yyyy")
            )
          ) : (
            <span>Zeitraum wählen</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          initialFocus
          mode="range"
          defaultMonth={date?.from}
          selected={date}
          onSelect={handleSelect}
          numberOfMonths={numberOfMonths}
          className="rounded-md border"
        />
      </PopoverContent>
    </Popover>
  );
}