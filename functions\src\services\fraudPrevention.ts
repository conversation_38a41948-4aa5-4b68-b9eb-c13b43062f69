import * as crypto from 'crypto';
import * as admin from 'firebase-admin';
import { serverTimestamp } from '../index';
import { Timestamp } from 'firebase-admin/firestore';

/**
 * FraudPreventionService - GDPR-compliant account abuse prevention 
 * Uses pseudonymized tokens to prevent abuse without storing personal data
 * Simplified to use only email address for maximum GDPR compliance
 */
export class FraudPreventionService {
  private db: FirebaseFirestore.Firestore;
  private readonly COLLECTION = 'accountTokens';
  private readonly TOKEN_EXPIRY_DAYS = 180; // 6 months retention

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Generates a pseudonymized hash token based on email
   * The token cannot be reversed to identify a specific user
   */
  private generateToken(email: string): string {
    // Normalize email by converting to lowercase
    const normalizedEmail = email.toLowerCase().trim();
    
    // Create a hash that cannot be reversed but will be consistent for the same input
    // Using SHA-256 for strong cryptographic hashing
    return crypto.createHash('sha256').update(normalizedEmail).digest('hex');
  }

  /**
   * Stores token with expiration date when a user deletes their account
   * GDPR compliant as we don't store PII, just a hash that can't identify the user
   */
  async storeDeletedAccountToken(email: string): Promise<void> {
    const token = this.generateToken(email);
    
    // Set expiration date (6 months from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + this.TOKEN_EXPIRY_DAYS);
    
    // Store the token with expiration date
    await this.db.collection(this.COLLECTION).doc(token).set({
      token,
      createdAt: serverTimestamp(),
      expiresAt: Timestamp.fromDate(expiresAt),
    });
  }

  /**
   * Checks if a token exists for the given email
   * Returns true if a matching token is found (indicating potential abuse)
   */
  async checkTokenExists(email: string): Promise<boolean> {
    const token = this.generateToken(email);
    
    // Check if token exists in database
    const doc = await this.db.collection(this.COLLECTION).doc(token).get();
    return doc.exists;
  }

  /**
   * Delete expired tokens (scheduled task)
   * This ensures we don't store tokens longer than necessary (GDPR compliance)
   */
  async purgeExpiredTokens(): Promise<number> {
    const now = Timestamp.now();
    
    const snapshot = await this.db.collection(this.COLLECTION)
      .where('expiresAt', '<', now)
      .get();
    
    const batch = this.db.batch();
    let count = 0;
    
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
      count++;
    });
    
    if (count > 0) {
      await batch.commit();
    }
    
    return count;
  }
} 