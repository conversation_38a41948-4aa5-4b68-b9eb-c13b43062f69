'use client';

import { useState } from 'react';
import { functions, db } from '@/app/firebase';
import { httpsCallable } from 'firebase/functions';
import { doc, getDoc } from 'firebase/firestore';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, FileText, GitBranch } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/components/providers/auth-provider';
import { RepoSelector } from './github/repo-selector';
import { ReadmeFetcher } from './github/readme-fetcher';
import { IssuesFetcher } from './github/issues-fetcher';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import MultiStoryDisplay from './multi-story-display';

// Types for feedback splitting
interface FeedbackItem {
  text: string;
  title: string;
}

interface FeedbackSplittingResponse {
  isMultiple: boolean;
  feedbackItems: FeedbackItem[];
  summary?: string;
}

// Type definition for the user story response (legacy single story)
interface UserStory {
  title: string;
  role: string;
  goal: string;
  benefit: string;
  formattedStory: string;
  feasibility: number;
  complexity: number;
  priority: "Critical" | "High" | "Medium" | "Low";
  featureCategory: string;
  isRejected?: boolean;
  rejectionReason?: string;
  duplicateIssueNumber?: number;
  duplicateIssueTitle?: string;
}

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
}

export default function StoryGenerator() {
  const { user } = useAuth();

  const [feedbackText, setFeedbackText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userStory, setUserStory] = useState<UserStory | null>(null);
  const [userStories, setUserStories] = useState<UserStory[]>([]);
  const [isMultipleStories, setIsMultipleStories] = useState(false);
  const [storiesSummary, setStoriesSummary] = useState<string>('');

  // GitHub integration
  const [selectedRepo, setSelectedRepo] = useState<Repository | null>(null);
  const [readmeContent, setReadmeContent] = useState('');
  const [issuesContent, setIssuesContent] = useState('');
  const [isCreatingIssue, setIsCreatingIssue] = useState(false);

  const handleRepoSelect = (repo: Repository) => {
    setSelectedRepo(repo);
  };

  const handleReadmeContent = (content: string) => {
    setReadmeContent(content);
  };

  const handleIssuesContent = (content: string) => {
    setIssuesContent(content);
  };

  const createGitHubIssue = async (story?: UserStory) => {
    const targetStory = story || userStory;

    if (!selectedRepo || !targetStory || !user) {
      toast.error('Repository und User Story sind erforderlich, um ein GitHub Issue zu erstellen.');
      return;
    }

    if (targetStory.isRejected) {
      toast.error('Abgelehnte User Stories können nicht als GitHub Issues erstellt werden.');
      return;
    }

    setIsCreatingIssue(true);

    try {
      // Get the GitHub token from Firestore
      const userProfileRef = doc(db, "userProfiles", user.uid);
      const userProfileSnap = await getDoc(userProfileRef);

      if (!userProfileSnap.exists() || !userProfileSnap.data().githubToken) {
        toast.error("Kein GitHub-Token gefunden. Bitte melden Sie sich erneut an.");
        return;
      }

      const githubToken = userProfileSnap.data().githubToken;

      // Determine labels based on priority and feature category
      const labels = [];

      // Priority-based labels
      switch (targetStory.priority) {
        case 'Critical':
          labels.push('priority: critical');
          break;
        case 'High':
          labels.push('priority: high');
          break;
        case 'Medium':
          labels.push('priority: medium');
          break;
        case 'Low':
          labels.push('priority: low');
          break;
      }

      // Add feature category as label (normalized)
      if (targetStory.featureCategory) {
        const categoryLabel = targetStory.featureCategory.toLowerCase().replace(/\s+/g, '-');
        labels.push(`feature: ${categoryLabel}`);
      }

      // Add user story label
      labels.push('user-story');

      // Create issue body with all relevant information
      const issueBody = `${targetStory.formattedStory}

## Bewertung

- **Machbarkeit**: ${targetStory.feasibility}/5
- **Komplexität**: ${targetStory.complexity}/10
- **Priorität**: ${targetStory.priority}
- **Feature-Kategorie**: ${targetStory.featureCategory}

## Details

**Rolle**: ${targetStory.role}
**Ziel**: ${targetStory.goal}
**Nutzen**: ${targetStory.benefit}

---
*Automatisch generiert aus Nutzerfeedback*`;

      // Create the GitHub issue
      const response = await fetch(`https://api.github.com/repos/${selectedRepo.full_name}/issues`, {
        method: 'POST',
        headers: {
          'Authorization': `token ${githubToken}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: targetStory.title,
          body: issueBody,
          labels: labels
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`GitHub API error: ${response.status} - ${errorData.message || 'Unknown error'}`);
      }

      const createdIssue = await response.json();

      toast.success(`GitHub Issue #${createdIssue.number} erfolgreich erstellt!`);

    } catch (error) {
      console.error('Error creating GitHub issue:', error);
      toast.error('Fehler beim Erstellen des GitHub Issues. Bitte versuchen Sie es erneut.');
    } finally {
      setIsCreatingIssue(false);
    }
  };

  const generateUserStory = async () => {
    if (!feedbackText.trim()) {
      toast.error('Bitte geben Sie einen Feedback-Text ein, um eine User Story zu generieren.');
      return;
    }

    setIsLoading(true);
    setUserStory(null);
    setUserStories([]);
    setIsMultipleStories(false);
    setStoriesSummary('');

    try {
      // Step 1: Split feedback into individual items
      const splitFeedbackFunction = httpsCallable<
        { feedbackText: string },
        FeedbackSplittingResponse
      >(functions, 'splitFeedback');

      const splitResult = await splitFeedbackFunction({
        feedbackText,
      });

      const splittingResponse = splitResult.data;

      // Step 2: Process each feedback item through user story generation
      const finalContext = selectedRepo
        ? `Repository: ${selectedRepo?.full_name}\n${readmeContent}`
        : '';
      const generatedStories: UserStory[] = [];

      const generateStoryFunction = httpsCallable<
        { feedbackText: string; projectContext: string; issuesContext: string },
        UserStory
      >(functions, 'generateUserStory');

      for (const feedbackItem of splittingResponse.feedbackItems) {
        const result = await generateStoryFunction({
          feedbackText: feedbackItem.text,
          projectContext: finalContext,
          issuesContext: issuesContent,
        });

        generatedStories.push(result.data);
      }

      // Update state based on results
      if (splittingResponse.isMultiple) {
        setIsMultipleStories(true);
        setUserStories(generatedStories);
        setStoriesSummary(splittingResponse.summary || '');
        setUserStory(null);
        toast.success(`${generatedStories.length} User Stories wurden erfolgreich erstellt.`);
      } else {
        setIsMultipleStories(false);
        setUserStory(generatedStories[0]);
        setUserStories([]);
        setStoriesSummary('');
        toast.success('Ihre User Story wurde erfolgreich erstellt.');
      }
    } catch (error) {
      console.error('Error generating user story:', error);
      toast.error('Fehler beim Generieren der User Story. Bitte versuchen Sie es erneut.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      <Card className="w-full shadow-sm h-fit col-span-1">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-2 text-2xl">
            <FileText className="h-6 w-6" />
            Feedback zu User Story
          </CardTitle>
          <CardDescription className="text-base mt-1">
            Wandeln Sie rohes Nutzerfeedback in strukturierte User Stories um
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="text-sm font-medium mb-2 block">Feedback-Text</label>
            <Textarea
              placeholder="Fügen Sie hier Nutzerfeedback oder Beobachtungen ein..."
              className="min-h-40 text-base resize-y"
              value={feedbackText}
              onChange={(e) => setFeedbackText(e.target.value)}
              disabled={isLoading}
            />
          </div>

          {user && (
            <div className="space-y-4 pt-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <GitBranch className="h-4 w-4" />
                  <h3 className="font-medium">GitHub Repository</h3>
                </div>
                <div className="w-1/2">
                  <RepoSelector
                    onRepoSelect={handleRepoSelect}
                    selectedRepo={selectedRepo}
                  />
                </div>
              </div>

              {selectedRepo && (
                <>
                  <ReadmeFetcher
                    selectedRepo={selectedRepo}
                    onReadmeContent={handleReadmeContent}
                  />
                  <IssuesFetcher
                    selectedRepo={selectedRepo}
                    onIssuesContent={handleIssuesContent}
                  />
                </>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="pt-2 pb-6">
          <Button
            onClick={generateUserStory}
            disabled={isLoading}
            size="lg"
            className="text-base px-6"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Generiere...
              </>
            ) : (
              'User Story generieren'
            )}
          </Button>
        </CardFooter>
      </Card>

      {isMultipleStories ? (
        <div className="md:col-span-2">
          <MultiStoryDisplay
            stories={userStories}
            summary={storiesSummary}
            selectedRepo={selectedRepo}
            onCreateIssue={createGitHubIssue}
          />
        </div>
      ) : userStory ? (
        <Card className="w-full shadow-sm overflow-hidden md:col-span-2">
          <CardHeader className="pb-3 border-b">
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-xl font-bold">{userStory.title}</CardTitle>
                <CardDescription className="mt-1 text-sm">
                  {userStory.isRejected ? 'Abgelehnte User Story' : 'Generierte User Story'}
                </CardDescription>
              </div>
              {!userStory.isRejected && (
                <Badge className={
                  userStory.priority === 'Critical' ? 'bg-red-100 text-red-800 hover:bg-red-200' :
                  userStory.priority === 'High' ? 'bg-orange-100 text-orange-800 hover:bg-orange-200' :
                  userStory.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' :
                  'bg-green-100 text-green-800 hover:bg-green-200'
                }>
                  {userStory.priority}
                </Badge>
              )}
              {userStory.isRejected && (
                <Badge variant="destructive">Abgelehnt</Badge>
              )}
            </div>
          </CardHeader>
          <CardContent className="py-4 space-y-4">
            {userStory.isRejected ? (
              <div className="p-4 rounded-lg bg-destructive/10 border border-destructive/30">
                <h3 className="text-lg font-semibold mb-2">Ablehnung der User Story</h3>
                <p className="text-sm whitespace-pre-line leading-relaxed">{userStory.rejectionReason}</p>
                {userStory.duplicateIssueNumber && (
                  <div className="mt-3 p-3 rounded-lg bg-background border">
                    <div className="flex items-center gap-2">
                      <GitBranch className="h-4 w-4" />
                      <span className="text-sm font-medium">Issue #{userStory.duplicateIssueNumber}</span>
                    </div>
                    <p className="text-sm mt-1">{userStory.duplicateIssueTitle}</p>
                  </div>
                )}
              </div>
            ) : (
              <>
                <div className="p-3 rounded-lg bg-muted/40 border">
                  <p className="text-sm whitespace-pre-line leading-relaxed">{userStory.formattedStory}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-sm font-medium mb-1">Rolle</h4>
                    <p className="text-sm">{userStory.role}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-sm font-medium mb-1">Ziel</h4>
                    <p className="text-sm">{userStory.goal}</p>
                  </div>
                  <div className="p-3 rounded-lg border bg-background">
                    <h4 className="text-sm font-medium mb-1">Nutzen</h4>
                    <p className="text-sm">{userStory.benefit}</p>
                  </div>
                </div>

                <Separator className="my-1" />

                {/* Evaluation section */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Story-Bewertung</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Feasibility */}
                    <div className="rounded-lg border p-3">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="text-sm font-semibold">Machbarkeit</h4>
                        <div className="flex items-center gap-1">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <div
                              key={i}
                              className={`w-6 h-6 flex items-center justify-center rounded text-xs ${
                                i < userStory.feasibility ? 'bg-primary text-primary-foreground' : 'bg-muted'
                              }`}
                            >
                              {i + 1}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Complexity */}
                    <div className="rounded-lg border p-3">
                      <div className="flex justify-between items-center">
                        <h4 className="text-sm font-semibold">Komplexität</h4>
                        <span className="px-2 py-1 text-xs rounded-full bg-muted font-medium">
                          {userStory.complexity}/10
                        </span>
                      </div>
                    </div>

                    {/* Feature Category */}
                    <div className="rounded-lg border p-3 md:col-span-2">
                      <h4 className="text-sm font-semibold mb-1">Feature-Kategorie</h4>
                      <p className="text-sm">{userStory.featureCategory}</p>
                    </div>
                  </div>

                  {/* Show which context was used */}
                  {selectedRepo && (readmeContent || issuesContent) && (
                    <div className="rounded-lg border p-3 mt-3 bg-muted/20">
                      <div className="flex items-center gap-2">
                        <GitBranch className="h-4 w-4" />
                        <h4 className="text-sm font-semibold">Repository-Kontext verwendet</h4>
                      </div>
                      <p className="mt-1 text-xs">
                        Die Bewertung basiert auf dem Kontext aus dem Repository <strong>{selectedRepo.full_name}</strong>
                        {readmeContent && issuesContent && ' (README + GitHub Issues)'}
                        {readmeContent && !issuesContent && ' (README)'}
                        {!readmeContent && issuesContent && ' (GitHub Issues)'}
                        .
                      </p>
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
          <CardFooter className="py-3 border-t flex justify-end space-x-3">
            {!userStory.isRejected && selectedRepo && (
              <Button
                variant="default"
                size="lg"
                onClick={() => createGitHubIssue()}
                disabled={isCreatingIssue}
              >
                {isCreatingIssue ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Erstelle Issue...
                  </>
                ) : (
                  'GitHub Issue erstellen'
                )}
              </Button>
            )}
            <Button variant="outline" size="lg" onClick={() => {
              let fullText = '';

              if (userStory.isRejected) {
                fullText = `
ABGELEHNTE USER STORY: ${userStory.title}

${userStory.rejectionReason}
${userStory.duplicateIssueNumber ? `Duplicate Issue #${userStory.duplicateIssueNumber}: ${userStory.duplicateIssueTitle}` : ''}
                `.trim();
              } else {
                fullText = `
${userStory.title}

${userStory.formattedStory}

Bewertung:
- Machbarkeit: ${userStory.feasibility}/5
- Komplexität: ${userStory.complexity}/10
- Priorität: ${userStory.priority}
- Feature-Kategorie: ${userStory.featureCategory}
                `.trim();
              }

              navigator.clipboard.writeText(fullText);
              toast.success("User Story in die Zwischenablage kopiert");
            }}>
              In Zwischenablage kopieren
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="md:col-span-2 flex items-center justify-center h-full">
          <Card className="w-full shadow-sm p-6 text-center border-dashed border-2">
            <div className="flex flex-col items-center justify-center space-y-3 text-muted-foreground">
              <FileText className="h-10 w-10" />
              <p className="text-base">Generieren Sie eine User Story, um das Ergebnis hier zu sehen</p>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}