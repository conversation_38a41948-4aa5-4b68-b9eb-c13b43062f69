"use client";

import * as React from "react";
import {
  LayoutDashboard,
  MessageSquare,
  Send,
  Mail,
  Settings,
} from "lucide-react";
import { useAuth } from "@/components/providers/auth-provider";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { NavAdmin } from "@/components/nav-admin";
import { doc, onSnapshot } from "firebase/firestore";
import { db } from "@/app/firebase";
import { useEffect, useState } from "react";
import { ThemeToggle } from "@/components/theme-toggle";
import { Separator } from "@/components/ui/separator";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { NavSecondary } from "./nav-secondary";
import { useIsMobile } from "@/hooks/use-mobile";
import { UserProfile, Organization } from "@/types";
import { OrganizationSwitcher } from "./organization-switcher";
import { NavMain } from "./nav-main";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, activeOrganizationId } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (!user?.uid) return;
    const userProfileDocRef = doc(db, "userProfiles", user.uid);
    const unsubscribeProfile = onSnapshot(userProfileDocRef, (doc) => {
      if (doc.exists()) {
        setUserProfile(doc.data() as UserProfile);
      } else {
        console.log("User profile not found for UID:", user.uid);
        setUserProfile(null);
      }
    });
    return () => unsubscribeProfile();
  }, [user]);

  useEffect(() => {
    if (!activeOrganizationId) {
      setOrganization(null);
      return;
    }

    const orgDocRef = doc(db, "organizations", activeOrganizationId);
    const unsubscribeOrg = onSnapshot(orgDocRef, (doc) => {
      if (doc.exists()) {
        setOrganization(doc.data() as Organization);
      } else {
        console.log("Organization not found for ID:", activeOrganizationId);
        setOrganization(null);
      }
    });
    return () => unsubscribeOrg();
  }, [activeOrganizationId]);

  const data = {
    user: {
      name: userProfile?.displayName || "Loading...",
      email: userProfile?.email || user?.email || "",
      avatar: userProfile?.avatar || "",
    },
    organization: [
      {
        name: "Manage",
        url: "/app/organizations/manage",
        icon: Settings,
      },
    ],
    main: [
      {
        name: "Dashboard",
        url: "/app/dashboard",
        icon: LayoutDashboard,
      },
    ],
    navSecondary: [
      {
        title: "Feedback",
        url: "/app/feedback",
        icon: Send,
        type: "dialog" as const,
      },
      {
        title: "Contact",
        url: "mailto:<EMAIL>",
        icon: Mail,
        type: "link" as const,
      },
    ],
  };

  return (
    <Sidebar collapsible="icon" side={isMobile ? "right" : "left"} {...props}>
      <SidebarHeader className="mt-1 px-2">
        <OrganizationSwitcher />
      </SidebarHeader>
      <SidebarContent className="space-y-0">
        <NavProjects title="" projects={data.main} />
        <NavProjects title="Organization" projects={data.organization} className="-mt-1" />
        <NavAdmin />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        {isMobile && (
          <>
            <ThemeToggle className="mx-auto mb-2" />
            <Separator />
          </>
        )}
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
