'use client';

import { useEffect, useState } from 'react';
import { db } from '@/app/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { useAuth } from '@/components/providers/auth-provider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
}

interface RepoSelectorProps {
  onRepoSelect: (repo: Repository) => void;
  selectedRepo: Repository | null;
}

export function RepoSelector({ onRepoSelect, selectedRepo }: RepoSelectorProps) {
  const { user } = useAuth();
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    async function fetchRepositories() {
      if (!user) return;
      
      setIsLoading(true);
      try {
        // Get the GitHub token from Firestore
        const userProfileRef = doc(db, "userProfiles", user.uid);
        const userProfileSnap = await getDoc(userProfileRef);
        
        if (!userProfileSnap.exists() || !userProfileSnap.data().githubToken) {
          toast.error("Kein GitHub-Token gefunden. Bitte melden Sie sich erneut an.");
          return;
        }
        
        const githubToken = userProfileSnap.data().githubToken;
        
        // Fetch repositories from GitHub API
        const response = await fetch('https://api.github.com/user/repos', {
          headers: {
            'Authorization': `token ${githubToken}`,
            'Accept': 'application/vnd.github.v3+json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`GitHub API error: ${response.status}`);
        }
        
        const repos = await response.json();
        setRepositories(repos);
      } catch (error) {
        console.error('Error fetching repositories:', error);
        toast.error('Fehler beim Laden der Repositories. Bitte versuchen Sie es erneut.');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchRepositories();
  }, [user]);

  return (
    <div className="space-y-2">
      <label className="text-sm font-medium block">GitHub Repository</label>
      {isLoading ? (
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm text-muted-foreground">Lade Repositories...</span>
        </div>
      ) : repositories.length > 0 ? (
        <Select
          value={selectedRepo?.full_name || ''}
          onValueChange={(value) => {
            const repo = repositories.find(r => r.full_name === value);
            if (repo) {
              onRepoSelect(repo);
            }
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Repository auswählen" />
          </SelectTrigger>
          <SelectContent>
            {repositories.map((repo) => (
              <SelectItem key={repo.id} value={repo.full_name}>
                {repo.private ? '🔒 ' : ''}
                {repo.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ) : (
        <p className="text-sm text-muted-foreground">
          {user ? 'Keine Repositories gefunden.' : 'Bitte melden Sie sich mit GitHub an, um Repositories zu laden.'}
        </p>
      )}
    </div>
  );
} 