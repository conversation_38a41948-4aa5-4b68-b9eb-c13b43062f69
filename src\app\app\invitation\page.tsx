"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { doc, getDoc } from "firebase/firestore";
import { auth, db, functions } from "@/app/firebase";
import { httpsCallable } from "firebase/functions";
import { onAuthStateChanged } from "firebase/auth";
import { Invitation } from "@/types";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDistanceToNow } from "date-fns";

export default function InvitationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const invitationId = searchParams.get("id");
  
  const [invitation, setInvitation] = useState<Invitation | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser);
      
      // Redirect to login if not authenticated
      if (!currentUser) {
        router.push(`/login?redirect=/app/invitation?id=${invitationId}`);
      }
    });

    return () => unsubscribe();
  }, [router, invitationId]);

  // Fetch invitation data
  useEffect(() => {
    const fetchInvitation = async () => {
      if (!invitationId) {
        setError("Keine Einladungs-ID gefunden");
        setIsLoading(false);
        return;
      }

      try {
        const invitationDoc = await getDoc(doc(db, "invitations", invitationId));
        
        if (!invitationDoc.exists()) {
          setError("Einladung nicht gefunden");
          setIsLoading(false);
          return;
        }

        const invitationData = invitationDoc.data() as Invitation;
        
        // Check if invitation has expired
        if (invitationData.expiresAt && new Date(invitationData.expiresAt.toDate()) < new Date()) {
          setError("Diese Einladung ist abgelaufen");
          setIsLoading(false);
          return;
        }

        // Check if invitation has already been accepted
        if (invitationData.status !== "pending") {
          setError("Diese Einladung wurde bereits verwendet oder storniert");
          setIsLoading(false);
          return;
        }

        setInvitation(invitationData);
        setIsLoading(false);
      } catch (err) {
        console.error("Error fetching invitation:", err);
        setError("Fehler beim Laden der Einladung");
        setIsLoading(false);
      }
    };

    // Only fetch if user is authenticated
    if (user) {
      fetchInvitation();
    }
  }, [invitationId, user]);

  const handleAcceptInvitation = async () => {
    if (!invitation || !invitationId || !user) return;

    setIsAccepting(true);
    try {
      // Check if user email matches invitation email
      if (user.email !== invitation.email) {
        toast.error(`Diese Einladung ist für ${invitation.email} bestimmt. Bitte melden Sie sich mit diesem Konto an.`);
        setIsAccepting(false);
        return;
      }

      // Call accept invitation function
      const acceptInvitationFn = httpsCallable(functions, "acceptInvitation");
      const result = await acceptInvitationFn({ invitationId });
      
      toast.success("Einladung erfolgreich angenommen!");
      
      // Redirect to dashboard after accepting
      setTimeout(() => {
        router.push("/app/dashboard");
      }, 1500);
    } catch (err: any) {
      console.error("Error accepting invitation:", err);
      toast.error(err?.message || "Fehler beim Annehmen der Einladung");
      setIsAccepting(false);
    }
  };

  // If not loaded yet, show loading state
  if (isLoading && !error) {
    return (
      <div className="container mx-auto py-10 max-w-md">
        <Card>
          <CardHeader>
            <CardTitle>
              <Skeleton className="h-8 w-3/4" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-full mt-2" />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10 max-w-md">
      <Card>
        <CardHeader>
          <CardTitle>
            {error ? "Einladung nicht verfügbar" : "Einladung zur Organisation"}
          </CardTitle>
          <CardDescription>
            {error ? error : `Sie wurden eingeladen, ${invitation?.organizationName} beizutreten`}
          </CardDescription>
        </CardHeader>
        
        {error ? (
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Die angeforderte Einladung ist nicht verfügbar. Möglicherweise wurde sie bereits verwendet,
              ist abgelaufen oder wurde storniert.
            </p>
          </CardContent>
        ) : (
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium">Organisation</p>
                <p className="text-sm text-muted-foreground">{invitation?.organizationName}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Ihre Rolle</p>
                <p className="text-sm text-muted-foreground capitalize">{invitation?.role}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Eingeladen von</p>
                <p className="text-sm text-muted-foreground">{invitation?.invitedByEmail}</p>
              </div>
              <div>
                <p className="text-sm font-medium">Gültig bis</p>
                <p className="text-sm text-muted-foreground">
                  {invitation?.expiresAt && formatDistanceToNow(
                    new Date(invitation.expiresAt.toDate()),
                    { addSuffix: true }
                  )}
                </p>
              </div>
              {user && user.email !== invitation?.email && (
                <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
                  <p className="text-sm text-yellow-800">
                    Diese Einladung ist für <strong>{invitation?.email}</strong> bestimmt. 
                    Sie sind derzeit als <strong>{user.email}</strong> angemeldet.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        )}
        
        <CardFooter className="flex justify-between">
          {!error && (
            <>
              <Button variant="outline" onClick={() => router.push("/app/dashboard")}>
                Ablehnen
              </Button>
              <Button
                onClick={handleAcceptInvitation}
                disabled={isLoading || isAccepting || !!error || (user && user.email !== invitation?.email)}
              >
                {isAccepting ? "Wird akzeptiert..." : "Einladung annehmen"}
              </Button>
            </>
          )}
          {error && (
            <Button onClick={() => router.push("/app/dashboard")} className="w-full">
              Zurück zum Dashboard
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
} 