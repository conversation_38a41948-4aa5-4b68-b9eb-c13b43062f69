/**
 * Creates a prompt to transform feedback into a structured user story
 */
export function createStoryPrompt(feedbackText: string, projectContext: string, issuesContext: string = ''): string {
  return `
    You are an expert AI assistant specializing in software development and product management.
    Your primary function is to meticulously analyze user feedback and contextual information (project context, existing GitHub issues)
    to generate precise and actionable user stories in German, or to identify and reject requests that are duplicates of existing issues.
    The accuracy, clarity, and adherence to the specified output format of your response are critical for the development workflow.

    Transformiere das folgende Nutzerfeedback in eine gut strukturierte User Story auf Deutsch ODER lehne es ab, wenn es ein Duplikat ist:

    FEEDBACK:
    "${feedbackText}"

    PROJEKT-KONTEXT:
    "${projectContext}"

    GITHUB ISSUES KONTEXT (eine Liste von bestehenden Issues, auf Duplikate prüfen):
    "${issuesContext}"

    Anweisungen:
    1. Analysiere das Feedback und extrahiere den wichtigsten Nutzerbedarf oder die Funktionsanfrage.
    2. **Duplikatprüfung**: Vergleiche den extrahierten Bedarf sorgfältig mit dem GITHUB ISSUES KONTEXT.
       - Eine User Story gilt als Duplikat, wenn ihr Kernzweck, ihr Ziel und der resultierende Nutzen semantisch identisch mit einem bestehenden Issue im GITHUB ISSUES KONTEXT sind. Geringfügige Unterschiede im Wortlaut spielen keine Rolle, solange der Inhalt und Sinn gleich sind.
       - Wenn ein Duplikat identifiziert wird, fahre mit den Anweisungen unter "Duplikat-Erkennung und Ablehnung" fort. Erstelle KEINE neue User Story.
    3. Wenn KEIN Duplikat gefunden wird: Erstelle eine User Story im Format "Als [ROLLE] möchte ich [ZIEL], damit [NUTZEN]".
    4. Schlage einen prägnanten, beschreibenden Titel für die Story vor (falls keine Ablehnung).
    5. Falls das Feedback unklar ist und keine klare Funktionsanfrage oder ein Duplikat identifiziert werden kann, erstelle die wahrscheinlichste User Story basierend auf dem Feedback und dem Projekt-Kontext.
    6. Konzentriere dich auf den wichtigsten Punkt, wenn das Feedback mehrere Anfragen enthält.
    7. Führe eine umfassende Bewertung der Story durch (falls keine Ablehnung), basierend auf den unten beschriebenen Bewertungskriterien.
    8. Bei Nicht-Duplikaten: Prüfe auch den GITHUB ISSUES KONTEXT auf thematisch ähnliche Issues (offen oder geschlossen).
       - Bewerte, ob die neue User Story bestehende Arbeit ergänzt oder damit in Konflikt steht.
       - Berücksichtige dies in deiner Machbarkeits- und Prioritätsbewertung und erwähne es in der Issue-Analyse.

    Duplikat-Erkennung und Ablehnung:
    1. Wenn die User Story, die aus dem FEEDBACK abgeleitet wurde, inhaltlich und sinngemäß einem bestehenden Issue im GITHUB ISSUES KONTEXT entspricht:
       - Setze "isRejected" auf true.
       - Formuliere den "rejectionReason" klar und informativ. Beispiel: "Diese Funktionalität ist bereits durch Issue #[Nummer des gefundenen duplizierten Issues] ('[Titel des gefundenen duplizierten Issues]') im bereitgestellten GitHub Issues Kontext abgedeckt."
       - Extrahiere die exakte Nummer des duplizierten Issues aus dem GITHUB ISSUES KONTEXT und setze sie in "duplicateIssueNumber".
       - Extrahiere den exakten Titel des duplizierten Issues aus dem GITHUB ISSUES KONTEXT und setze ihn in "duplicateIssueTitle".
       - Setze einen aussagekräftigen "title" für die abgelehnte Story.
       - Alle anderen Felder (role, goal, benefit, formattedStory, feasibility, complexity, priority, featureCategory) werden NICHT benötigt, wenn isRejected true ist.
    2. Eine Story sollte NUR dann als Duplikat abgelehnt werden, wenn ihr Kernzweck und Ergebnis semantisch identisch mit einem bestehenden Issue sind.
    3. Bei teilweisen Überschneidungen oder Ähnlichkeiten, die aber keinen vollständigen Duplikat darstellen, sollte die Story NICHT abgelehnt werden. Stattdessen sollte die Beziehung zu bestehenden Issues in der Bewertungssektion "Issue-Analyse" erwähnt werden.

    Bewertungskriterien (nur anwenden, wenn "isRejected" nicht true ist):
    1. Machbarkeit (Feasibility): Bewerte auf einer Skala von 1-5, wie technisch machbar die Implementierung ist. Berücksichtige dabei auch ähnliche Issues und deren Status.
    2. Sinnhaftigkeit im Projektkontext (Project Context Alignment): Beurteile, ob und wie die Funktion mit den Gesamtprojektzielen, dem vorhandenen Funktionsumfang UND den GitHub Issues übereinstimmt. Prüfe auf Duplikate oder Konflikte.
    3. Komplexität (Complexity): Bewerte die Implementierungskomplexität auf einer Skala von 1-10. Berücksichtige dabei verwandte Issues und deren Komplexität.
    4. Priorität (Priority): Weise eine Prioritätsstufe (Critical, High, Medium, Low) basierend auf Benutzereinfluss, Geschäftswert UND Relation zu bestehenden Issues zu.
    5. Feature-Kategorisierung (Feature Categorization): Identifiziere, zu welcher bestehenden Funktionskategorie oder Epic diese Anfrage gehört, basierend auf Issues und Projektstruktur.
    6. **Issue-Analyse**: Wenn ähnliche (aber keine exakten Duplikate) Issues gefunden wurden, erwähne diese kurz in der Bewertung und erkläre die Beziehung.
  `.trim();
}