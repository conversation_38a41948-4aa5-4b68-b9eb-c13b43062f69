'use client';

import { useState, useEffect } from 'react';
import { db } from '@/app/firebase';
import { doc, getDoc } from 'firebase/firestore';
import { useAuth } from '@/components/providers/auth-provider';
import { toast } from 'sonner';
import { Loader2, AlertCircle, FileText } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface Repository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
}

interface ReadmeFetcherProps {
  selectedRepo: Repository | null;
  onReadmeContent: (content: string) => void;
}

export function ReadmeFetcher({ selectedRepo, onReadmeContent }: ReadmeFetcherProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [readmeContent, setReadmeContent] = useState<string | null>(null);

  useEffect(() => {
    async function fetchReadme() {
      if (!user || !selectedRepo) {
        setReadmeContent(null);
        setError(null);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Get the GitHub token from Firestore
        const userProfileRef = doc(db, "userProfiles", user.uid);
        const userProfileSnap = await getDoc(userProfileRef);
        
        if (!userProfileSnap.exists() || !userProfileSnap.data().githubToken) {
          setError("Kein GitHub-Token gefunden. Bitte melden Sie sich erneut an.");
          return;
        }
        
        const githubToken = userProfileSnap.data().githubToken;
        
        // Fetch README from GitHub API
        // First try to get the README through the dedicated endpoint
        let response = await fetch(`https://api.github.com/repos/${selectedRepo.full_name}/readme`, {
          headers: {
            'Authorization': `token ${githubToken}`,
            'Accept': 'application/vnd.github.v3+json'
          }
        });
        
        // If README not found through dedicated endpoint, try to get it from the contents API
        if (response.status === 404) {
          // Try common README filenames
          const fileNames = ['README.md', 'readme.md', 'Readme.md', 'README', 'readme'];
          let foundReadme = false;
          
          for (const fileName of fileNames) {
            response = await fetch(`https://api.github.com/repos/${selectedRepo.full_name}/contents/${fileName}`, {
              headers: {
                'Authorization': `token ${githubToken}`,
                'Accept': 'application/vnd.github.v3+json'
              }
            });
            
            if (response.ok) {
              foundReadme = true;
              break;
            }
          }
          
          if (!foundReadme) {
            setError(`Keine README-Datei in Repository "${selectedRepo.name}" gefunden.`);
            setReadmeContent(null);
            onReadmeContent('');
            return;
          }
        } else if (!response.ok) {
          throw new Error(`GitHub API error: ${response.status}`);
        }
        
        const readmeData = await response.json();
        
        // GitHub returns content as base64 encoded
        const content = readmeData.content ? atob(readmeData.content.replace(/\n/g, '')) : '';
        
        setReadmeContent(content);
        onReadmeContent(content);
      } catch (error) {
        console.error('Error fetching README:', error);
        setError('Fehler beim Laden der README-Datei. Bitte versuchen Sie es erneut.');
        setReadmeContent(null);
        onReadmeContent('');
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchReadme();
  }, [user, selectedRepo, onReadmeContent]);

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 mt-4">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Lade README-Datei...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mt-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Fehler</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (readmeContent) {
    return (
      <div className="mt-4">
        <div className="flex items-center space-x-2">
          <FileText className="h-4 w-4" />
          <span className="text-sm font-medium">README als Kontext geladen</span>
        </div>
      </div>
    );
  }

  return null;
} 