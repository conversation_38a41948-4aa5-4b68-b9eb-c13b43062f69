"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserStorySchema = void 0;
const genkit_1 = require("genkit");
// Flattened schema with conditional validation based on isRejected
exports.UserStorySchema = genkit_1.z.object({
    // Common fields
    isRejected: genkit_1.z.boolean().describe("Whether this story is rejected as a duplicate"),
    title: genkit_1.z.string().describe("A concise, descriptive title for the user story"),
    // Fields for accepted stories (optional in schema, but will be validated)
    role: genkit_1.z.string().optional().describe("The specific user role or persona who will benefit from this feature"),
    goal: genkit_1.z.string().optional().describe("What the user wants to accomplish"),
    benefit: genkit_1.z.string().optional().describe("The value or benefit the user will receive from this feature"),
    formattedStory: genkit_1.z.string().optional().describe("Complete user story in standard format: 'As a [role], I want to [goal], so that [benefit]'"),
    feasibility: genkit_1.z.number().min(1).max(5).optional().describe("Technical feasibility rating from 1 (very difficult) to 5 (very feasible)"),
    complexity: genkit_1.z.number().min(1).max(10).optional().describe("Implementation complexity from 1 (very simple) to 10 (extremely complex)"),
    priority: genkit_1.z.enum(["Critical", "High", "Medium", "Low"]).optional().describe("Business priority level for this feature"),
    featureCategory: genkit_1.z.string().optional().describe("Category or type of feature (e.g., 'Authentication', 'Reporting', 'User Management')"),
    issueAnalysis: genkit_1.z.string().optional().describe("Analysis of relationships to similar (but non-duplicate) existing issues"),
    // Fields for rejected stories (optional in schema, but will be validated)
    rejectionReason: genkit_1.z.string().optional().describe("Reason why this story was rejected"),
    duplicateIssueNumber: genkit_1.z.number().optional().describe("Issue number of the duplicate"),
    duplicateIssueTitle: genkit_1.z.string().optional().describe("Title of the duplicate issue"),
}).superRefine((data, ctx) => {
    // Validation logic: ensure the appropriate fields are filled based on isRejected
    if (data.isRejected === true) {
        // For rejected stories, ensure these fields are present
        if (!data.rejectionReason) {
            ctx.addIssue({
                code: genkit_1.z.ZodIssueCode.custom,
                message: "rejectionReason is required when isRejected is true",
                path: ["rejectionReason"]
            });
        }
        if (!data.duplicateIssueNumber) {
            ctx.addIssue({
                code: genkit_1.z.ZodIssueCode.custom,
                message: "duplicateIssueNumber is required when isRejected is true",
                path: ["duplicateIssueNumber"]
            });
        }
        if (!data.duplicateIssueTitle) {
            ctx.addIssue({
                code: genkit_1.z.ZodIssueCode.custom,
                message: "duplicateIssueTitle is required when isRejected is true",
                path: ["duplicateIssueTitle"]
            });
        }
    }
    else if (data.isRejected === false) {
        // For accepted stories, ensure these fields are present
        const requiredFields = [
            "role", "goal", "benefit", "formattedStory",
            "feasibility", "complexity", "priority", "featureCategory"
        ];
        for (const field of requiredFields) {
            if (!data[field]) {
                ctx.addIssue({
                    code: genkit_1.z.ZodIssueCode.custom,
                    message: `${field} is required when isRejected is false`,
                    path: [field]
                });
            }
        }
    }
});
//# sourceMappingURL=story-schema.js.map