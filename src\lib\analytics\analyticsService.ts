import { db } from "@/app/firebase";
import {
  collection,
  addDoc,
  serverTimestamp,
  doc,
  increment,
  setDoc,
  getDoc,
} from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";
import { COOKIE_CONSENT_KEY } from "@/lib/cookie-management";

// Constants for localStorage keys
const DEV_MODE_KEY = "analytics_dev_mode";

// Bot detection regex patterns
const BOT_USER_AGENT_PATTERNS = [
  /bot/i, /crawl/i, /spider/i, /headless/i, /lighthouse/i,
  /googlebot/i, /bingbot/i, /yandex/i, /baidu/i, /semrush/i,
  /ahrefsbot/i, /mj12bot/i, /dotbot/i, /rogerbot/i,
  /yahoo! slurp/i, /baiduspider/i, /seznambot/i, /petalbot/i,
  /duckduckbot/i, /adidxbot/i, /facebookexternalhit/i, /twitterbot/i,
  /chrome-lighthouse/i, /applebot/i, /pingdom/i, /gtmetrix/i,
  /phantomjs/i, /whatsapp/i, /slackbot/i, /discordbot/i,
  /screaming frog/i, /ahrefs/i
];

// Known bot IPs (example - maintain this list or use an API)
const BOT_IPS = [
  '*******',  // Example Google crawler IP
  '*******',  // Example Google crawler IP
];

// Function to check if the current path is an admin path
function isAdminPath(): boolean {
  if (typeof window === 'undefined') return false;
  return window.location.pathname.startsWith('/app/admin/');
}

// Function to detect if the current visitor is a bot
function isBot(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Check user agent
  const userAgent = navigator.userAgent || '';
  if (BOT_USER_AGENT_PATTERNS.some(pattern => pattern.test(userAgent))) {
    return true;
  }
  
  // Check for headless browser
  if (!navigator.webdriver && 
      'webdriver' in navigator && 
      (navigator as any).webdriver === true) {
    return true;
  }
  
  // Check for inconsistent browser properties often spoofed by bots
  if (userAgent.includes('Chrome') && !('chrome' in window)) {
    return true;
  }
  
  // Check for unusual screen dimensions that bots often use
  if (window.screen.width === 0 || window.screen.height === 0) {
    return true;
  }
  
  // Add more advanced detection if needed
  
  return false;
}

// Data validation function to check if data looks like it's from a bot
function validateBotActivityData(data: any): boolean {
  // Check for abnormal behavior
  
  // 1. Super fast navigation (less than 1 second per page view)
  if (data && data.navigationSpeed && data.navigationSpeed < 1000) {
    return false;
  }
  
  // 2. Unusual mouse movement patterns
  if (data && data.mousePosition && 
      data.mousePosition.every((pos: any) => pos.x % 10 === 0 && pos.y % 10 === 0)) {
    return false;
  }
  
  // Passed all checks
  return true;
}

// Check for suspicious navigation pattern
function hasSuspiciousNavigationPattern(timestamps: number[]): boolean {
  if (timestamps.length < 3) return false;
  
  // Calculate time intervals between actions
  const intervals: number[] = [];
  for (let i = 1; i < timestamps.length; i++) {
    intervals.push(timestamps[i] - timestamps[i-1]);
  }
  
  // Check for robotic patterns in timestamps
  
  // 1. Too regular intervals (bots often have precisely timed actions)
  const avgInterval = intervals.reduce((sum, val) => sum + val, 0) / intervals.length;
  const tooRegular = intervals.filter(interval => 
    Math.abs(interval - avgInterval) < (avgInterval * 0.1)
  ).length > (intervals.length * 0.8);
  
  if (tooRegular) return true;
  
  // 2. Unusually fast and consistent actions
  const allTooFast = intervals.every(interval => interval < 200);
  if (allTooFast && intervals.length > 5) return true;
  
  // 3. Perfect timing patterns (exactly the same interval multiple times)
  const hasRepeatedExactIntervals = intervals.some((int, i, arr) => 
    arr.filter(x => x === int).length > 2
  );
  
  if (hasRepeatedExactIntervals) return true;
  
  return false;
}

interface InteractionEvent {
  type: "scroll" | "click" | "mousemove" | "custom";
  path: string;
  timestamp: number;
  data: Record<string, any>;
  sessionId: string;
}

interface SessionData {
  startTime: number;
  pageViews: number;
  lastActivity: number;
}

// Throttle Funktion für Performance-Optimierung
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url, window.location.origin);
    // Wenn utm_id vorhanden ist, entferne alle anderen Parameter
    if (urlObj.searchParams.has('utm_id')) {
      const utmId = urlObj.searchParams.get('utm_id');
      urlObj.search = `?utm_id=${utmId}`;
    }
    return urlObj.pathname + urlObj.search;
  } catch (error) {
    return url;
  }
}

function getDeviceType(): 'desktop' | 'tablet' | 'mobile' {
  const width = window.innerWidth;
  if (width >= 1024) return 'desktop';
  if (width >= 768) return 'tablet';
  return 'mobile';
}

function normalizeSource(referrer: string): string {
  try {
    const url = new URL(referrer);
    // Extrahiere die Hauptdomain (z.B. facebook.com, google.com)
    return url.hostname.replace('www.', '');
  } catch {
    return 'direct';
  }
}

export const analyticsService = {
  sessionId: "",
  sessionData: new Map<string, SessionData>(),
  isBotVisitor: false,
  actionTimestamps: [] as number[],
  
  // Check if developer mode is enabled (to ignore own clicks)
  isDeveloperMode(): boolean {
    try {
      if (typeof window === 'undefined') return false;
      return localStorage.getItem(DEV_MODE_KEY) === "true";
    } catch (error) {
      console.error("Failed to check developer mode:", error);
      return false; // Safe fallback value
    }
  },
  
  // Enable developer mode (stop tracking own clicks)
  enableDeveloperMode(): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(DEV_MODE_KEY, "true");
    console.log("Developer mode enabled - your clicks won't be tracked");
  },
  
  // Disable developer mode (resume tracking)
  disableDeveloperMode(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(DEV_MODE_KEY);
    console.log("Developer mode disabled - your clicks will be tracked");
  },
  
  // Toggle developer mode
  toggleDeveloperMode(): void {
    if (this.isDeveloperMode()) {
      this.disableDeveloperMode();
    } else {
      this.enableDeveloperMode();
    }
  },

  async initialize() {
    // Automatically enable developer mode on admin pages
    if (isAdminPath()) {
      this.enableDeveloperMode();
      return;
    }
    
    if (process.env.NODE_ENV === 'development' || this.isDeveloperMode()) return;
    
    // Check if this is a bot before proceeding with tracking
    this.isBotVisitor = isBot();
    if (this.isBotVisitor) {
      console.debug("Bot visitor detected, analytics disabled");
      return;
    }

    this.sessionId = uuidv4();
    this.setupEventListeners();
    this.initializeSession();
    await this.trackVisit();

    // Track session duration when user leaves
    window.addEventListener('beforeunload', () => {
      this.trackSessionEnd();
    });

    // Track session duration when user switches tabs
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackSessionEnd();
      }
    });
  },

  initializeSession() {
    this.sessionData.set(this.sessionId, {
      startTime: Date.now(),
      pageViews: 1,
      lastActivity: Date.now()
    });
  },

  async trackSessionEnd() {
    if (this.isDeveloperMode()) return;
    
    const session = this.sessionData.get(this.sessionId);
    if (!session) return;

    const duration = Math.floor((Date.now() - session.startTime) / 1000); // in seconds
    const isBounce = session.pageViews === 1 && duration < 30; // bounce if single page view and less than 30 seconds

    const today = new Date().toISOString().split("T")[0];
    const currentPath = normalizeUrl(window.location.pathname + window.location.search);

    try {
      const dailyStatsRef = doc(db, "aggregated_stats", today);
      await setDoc(
        dailyStatsRef,
        {
          totalDuration: increment(duration),
          totalSessions: increment(1),
          bounces: increment(isBounce ? 1 : 0),
          paths: {
            [currentPath]: {
              totalDuration: increment(duration),
              totalSessions: increment(1),
              bounces: increment(isBounce ? 1 : 0),
            },
          },
        },
        { merge: true }
      );
    } catch (error) {
      console.error("Failed to track session end:", error);
    }
  },

  async trackVisit() {
    if (process.env.NODE_ENV === 'development' || this.isBotVisitor || this.isDeveloperMode() || isAdminPath()) return;

    const today = new Date().toISOString().split("T")[0];
    const currentPath = normalizeUrl(window.location.pathname + window.location.search);
    const source = normalizeSource(document.referrer);
    const deviceType = getDeviceType();
    
    const hasVisitedThisSession = sessionStorage.getItem('visited_this_session');
    const isFirstVisit = !hasVisitedThisSession;
    
    sessionStorage.setItem('visited_this_session', 'true');

    try {
      const dailyStatsRef = doc(db, "aggregated_stats", today);
      await setDoc(
        dailyStatsRef,
        {
          visits: increment(1),
          firstVisits: increment(isFirstVisit ? 1 : 0),
          lastUpdated: serverTimestamp(),
          sources: {
            [source]: increment(1)
          },
          devices: {
            [deviceType]: increment(1),
            browsers: {
              [navigator.userAgent]: increment(1)
            },
            os: {
              [navigator.platform]: increment(1)
            }
          },
          paths: {
            [currentPath]: {
              visits: increment(1),
              firstVisits: increment(isFirstVisit ? 1 : 0),
              lastVisit: serverTimestamp(),
              sources: {
                [source]: increment(1)
              },
              devices: {
                [deviceType]: increment(1)
              }
            },
          },
        },
        { merge: true }
      );
    } catch (error) {
      console.error("Failed to track visit:", error);
    }
  },

  async trackEvent(event: Omit<InteractionEvent, "timestamp">) {
    if (process.env.NODE_ENV === 'development' || 
        isAdminPath() || 
        this.isBotVisitor ||
        this.isDeveloperMode()) return;

    const hasConsent = localStorage.getItem(COOKIE_CONSENT_KEY) === "true";
    if (!hasConsent) return;
    
    // Record timestamp for bot pattern detection
    this.actionTimestamps.push(Date.now());
    
    // Check for suspicious navigation patterns (but don't store userAgent)
    const hasAbnormalNavigation = hasSuspiciousNavigationPattern(this.actionTimestamps);
    
    // Extra validation for bot-like behavior
    if (!validateBotActivityData(event.data)) {
      return;
    }

    try {
      await addDoc(collection(db, "analytics"), {
        ...event,
        timestamp: serverTimestamp(),
        pageInfo: {
          path: window.location.pathname,
          url: window.location.href,
          title: document.title,
          referrer: document.referrer,
        },
        screenSize: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        // Remove userAgent tracking for privacy
        // userAgent: navigator.userAgent,
        
        // Instead of storing identifiable information, store anonymous behavior data
        botCheckPerformed: true,
        // Only store the number of timestamps to save storage and preserve privacy
        actionCount: this.actionTimestamps.length,
        hasMouseMovement: window.hasOwnProperty('onmousemove'),
        // Store non-identifiable metrics for bot detection
        behaviorMetrics: {
          suspiciousNavigation: hasAbnormalNavigation,
          interactionTime: this.actionTimestamps.length > 1 
            ? this.actionTimestamps[this.actionTimestamps.length - 1] - this.actionTimestamps[0] 
            : 0,
          actionsPerMinute: this.actionTimestamps.length > 1 
            ? (this.actionTimestamps.length / ((this.actionTimestamps[this.actionTimestamps.length - 1] - this.actionTimestamps[0]) / 60000)) 
            : 0
        }
      });
    } catch (error) {
      console.error("Failed to track event:", error);
    }
  },

  setupEventListeners() {
    // Scroll Tracking
    window.addEventListener(
      "scroll",
      throttle(() => {
        const scrollPosition = {
          x: window.scrollX,
          y: window.scrollY,
          percentage:
            (window.scrollY /
              (document.documentElement.scrollHeight - window.innerHeight)) *
            100,
        };

        this.trackEvent({
          type: "scroll",
          path: normalizeUrl(window.location.pathname + window.location.search),
          sessionId: this.sessionId,
          data: scrollPosition,
        });
      }, 1000)
    );

    // Click Tracking - Removed trackEvent call to avoid logging every click
    window.addEventListener("click", (e: MouseEvent) => {
      // Optionally, keep updating timestamps if needed for bot detection, 
      // but don't send a full event record.
      // this.actionTimestamps.push(Date.now()); 

      // Original code that sent every click:
      /*
      const clickData = {
        xPercent: (e.clientX / window.innerWidth) * 100,
        yPercent: (e.clientY / window.innerHeight) * 100,
        timestamp: Date.now(),
      };

      this.trackEvent({
        type: "click",
        path: normalizeUrl(window.location.pathname + window.location.search),
        sessionId: this.sessionId,
        data: clickData,
      });
      */
    });
  },

  async trackCustomEvent(eventName: string, increment_by: number = 1, trackByDevice: boolean = false, trackReferrer: boolean = false) {
    if (process.env.NODE_ENV === 'development' || 
        isAdminPath() ||
        this.isDeveloperMode()) return;

    try {
      const today = new Date().toISOString().split("T")[0];
      const currentPath = normalizeUrl(window.location.pathname + window.location.search);
      const dailyStatsRef = doc(db, "aggregated_stats", today);
      
      const updateData: any = {
        customEvents: increment(increment_by),
        paths: {
          [currentPath]: {
            customEvents: increment(increment_by),
            [`events.${eventName}`]: increment(increment_by)
          }
        }
      };
      
      // Track device type if requested
      if (trackByDevice) {
        const deviceType = getDeviceType();
        updateData[`events.${eventName}.byDevice.${deviceType}`] = increment(increment_by);
        updateData.paths[currentPath][`events.${eventName}.byDevice.${deviceType}`] = increment(increment_by);
      }
      
      // Track referrer source if requested
      if (trackReferrer) {
        const source = normalizeSource(document.referrer);
        updateData[`events.${eventName}.bySource.${source}`] = increment(increment_by);
        updateData.paths[currentPath][`events.${eventName}.bySource.${source}`] = increment(increment_by);
      }
      
      await setDoc(
        dailyStatsRef,
        updateData,
        { merge: true }
      );
    } catch (error) {
      console.error("Failed to track custom event:", error);
    }
  }
};
