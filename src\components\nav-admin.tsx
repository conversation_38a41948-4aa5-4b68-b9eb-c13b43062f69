"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MessageCircle, ShieldAlert, type LucideIcon } from "lucide-react";
import { SidebarGroup, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useAuth } from "@/components/providers/auth-provider";

export function NavAdmin() {
  const pathname = usePathname();
  const { user, claims } = useAuth();

  // Only show for admin users
  if (!user || !claims?.admin) {
    return null;
  }

  const adminItems = [
    {
      name: "Error Dashboard",
      url: "/app/admin/error-dashboard",
      icon: AlertTriangle,
    },
    {
      name: "Analytics Dashboard",
      url: "/app/admin/analytics",
      icon: BarChart,
    },
    {
      name: "Feedback Dashboard",
      url: "/app/admin/feedback-dashboard",
      icon: MessageCircle,
    },
    // Weitere Admin-Menüpunkte hier hinz<PERSON><PERSON><PERSON>
  ];

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel className="text-red-500 flex items-center gap-1.5">
        <ShieldAlert className="h-4 w-4" />
        Admin
      </SidebarGroupLabel>
      <SidebarMenu>
        {adminItems.map((item) => (
          <SidebarMenuItem key={item.name}>
            <SidebarMenuButton asChild isActive={pathname === item.url}>
              <Link 
                href={item.url} 
                className="transition-all duration-200 ease-in-out hover:bg-red-500/10 hover:text-red-500 hover:scale-[1.02] hover:font-medium hover:shadow-sm dark:hover:bg-red-500/20 rounded-md"
              >
                <item.icon />
                <span>{item.name}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
} 