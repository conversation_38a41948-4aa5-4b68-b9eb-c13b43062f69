"use client";

import React from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import Link from "next/link";
import { ThemeToggle } from "@/components/theme-toggle";
import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface PathSegment {
  name: string;
  href: string;
  isLast: boolean;
}

function generateBreadcrumbs(pathname: string): PathSegment[] {
  // Entferne /app vom Anfang und splitte den Pfad
  const paths = pathname
    .replace(/^\/app\/?/, "")
    .split("/")
    .filter(Boolean);

 

  return paths.map((segment, index) => {
    // Erstelle den href-Pfad für dieses Segment
    const href = `/app/${paths.slice(0, index + 1).join("/")}`;

    // Formatiere den Segmentnamen (erste Buchstabe groß, - durch Leerzeichen ersetzen)
    const CUSTOM_NAMES: Record<string, string> = {
      
    };

    // In generateBreadcrumbs:
    const name =
      CUSTOM_NAMES[segment] ||
      segment
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");

    return {
      name,
      href,
      isLast: index === paths.length - 1,
    };
  });
}

export function AppHeader() {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname);
  const isMobile = useIsMobile();

  // Neue Funktion zur Filterung der Breadcrumbs für mobile Ansicht
  const visibleBreadcrumbs = isMobile
    ? breadcrumbs.slice(-1) // Nur die letzten 2 für Mobile
    : breadcrumbs;

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4 w-full">
        {!isMobile && <SidebarTrigger className="-ml-1" isMobile={isMobile} />}
        {!isMobile && <Separator orientation="vertical" className="mr-2 h-4" />}
        <Breadcrumb>
          <BreadcrumbList>
            {!isMobile && (
              <>
                <BreadcrumbItem key="item-main">
                  <BreadcrumbLink href="/app/dashboard">App</BreadcrumbLink>
                </BreadcrumbItem>
                {breadcrumbs.length > 0 && <BreadcrumbSeparator />}
              </>
            )}
            {visibleBreadcrumbs.map((segment, index) => (
              <React.Fragment key={`breadcrumb-${index}`}>
                {index > 0 && <BreadcrumbSeparator />}
                <BreadcrumbItem>
                  {segment.isLast ? (
                    <BreadcrumbPage>{segment.name}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={segment.href}>
                      {segment.name}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </React.Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
        <div className="flex-1" />
        
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="mr-2 max-md:hidden"
                aria-label="Kontakt"
                disabled
                onClick={() => window.location.href = "mailto:"}
              >
                Kontakt <Mail className="ml-1 h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>philipp</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <ThemeToggle className="max-md:hidden" />
        {isMobile && (
          <div className="flex justify-end">
            <SidebarTrigger className="ml-2" isMobile={isMobile} />
          </div>
        )}
      </div>
    </header>
  );
}
