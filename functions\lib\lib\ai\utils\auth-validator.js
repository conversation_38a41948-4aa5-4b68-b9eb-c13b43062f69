"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateAuth = exports.extractIdToken = void 0;
const https_1 = require("firebase-functions/v2/https");
const v2_1 = require("firebase-functions/v2");
const admin = __importStar(require("firebase-admin"));
/**
 * Extracts ID token from Authorization header
 */
function extractIdToken(authHeader) {
    return authHeader && authHeader.startsWith('Bearer ')
        ? authHeader.split('Bearer ')[1]
        : null;
}
exports.extractIdToken = extractIdToken;
/**
 * Validates user authentication token
 */
async function validateAuth(idToken) {
    if (!idToken) {
        throw new https_1.HttpsError("unauthenticated", "User must be authenticated");
    }
    try {
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        if (!decodedToken.email_verified) {
            throw new https_1.HttpsError("unauthenticated", "User must verify email first");
        }
        return decodedToken;
    }
    catch (error) {
        v2_1.logger.warn("Authentication failed", { error: String(error) });
        throw new https_1.HttpsError("unauthenticated", "Invalid authentication token");
    }
}
exports.validateAuth = validateAuth;
//# sourceMappingURL=auth-validator.js.map