import { initializeTestEnvironment, assertSucceeds, assertFails, RulesTestEnvironment } from '@firebase/rules-unit-testing';
import { doc, setDoc, getDoc, updateDoc, deleteDoc, Timestamp } from 'firebase/firestore';
import * as fs from 'fs';

jest.setTimeout(30000);

// Test environment and common variables
let testEnv: RulesTestEnvironment;
const projectId = 'o3domo';

const organizationId = 'test-org-123';
const anotherOrganizationId = 'another-org-456';
const managerId = 'test-manager-abc';
const adminId = 'test-admin-xyz';
const memberId = 'test-member-def';
const meterId = 'test-meter-xyz';
const readingId = 'test-reading-abc';

const meterPath = `meters/${meterId}`;

// Helper function to create auth contexts with appropriate roles
const getAuthContext = (uid: string, role: 'admin' | 'manager' | 'member', orgId: string = organizationId) => {
  return testEnv.authenticatedContext(uid, {
    activeOrganizationId: orgId,
    activeRole: role,
    organizations: { [orgId]: role },
  });
};

// Sample meter data
const createMeterData = () => ({
  organizationId,
  objectId: 'object-123',
  unitId: null,
  assignedLevel: 'Objekt',
  meterType: 'Strom',
  meterNumber: 'SN-0001',
  meterActive: true,
});

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId,
    firestore: {
      host: 'localhost',
      port: 8080,
      rules: fs.readFileSync('firestore.rules', 'utf8'),
    },
  });
});

afterEach(async () => {
  await testEnv.clearFirestore();
});

afterAll(async () => {
  await testEnv.cleanup();
});



// Test suite for Contracts
describe('Firestore Rules for Contracts', () => {
  let managerDb: any;
  let adminDb: any;
  let memberDb: any;
  let unauthDb: any;

  const contractId = 'test-contract-123';
  const objectId = 'test-object-123';
  const unitId = 'test-unit-456';
  const tenantId = 'test-tenant-789';
  const contractPath = `organizations/${organizationId}/contracts/${contractId}`;

  const createObjectData = () => ({
    organizationId,
    createdByUserId: managerId,
  });

  const createUnitData = () => ({
    organizationId,
    objectId,
    createdByUserId: managerId,
  });

  const createTenantData = () => ({
    organizationId,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  });

  const createContractData = () => ({
    organizationId,
    objectId,
    unitId,
    tenantId,
    createdAt: Timestamp.now(),
    updatedAt: Timestamp.now(),
  });

  beforeEach(async () => {
    // Set up required parent documents
    const managerContext = getAuthContext(managerId, 'manager');
    const db = managerContext.firestore();
    await setDoc(doc(db, `organizations/${organizationId}/objects/${objectId}`), createObjectData());
    await setDoc(doc(db, `organizations/${organizationId}/objects/${objectId}/units/${unitId}`), createUnitData());
    await setDoc(doc(db, `organizations/${organizationId}/tenants/${tenantId}`), createTenantData());
  });

  describe('As Manager', () => {
    beforeEach(() => {
      const context = getAuthContext(managerId, 'manager');
      managerDb = context.firestore();
    });

    it('should allow creating a contract', async () => {
      const contractRef = doc(managerDb, contractPath);
      await assertSucceeds(setDoc(contractRef, createContractData()));
    });

    it('should allow reading a contract', async () => {
      const contractRef = doc(managerDb, contractPath);
      await setDoc(contractRef, createContractData());
      await assertSucceeds(getDoc(contractRef));
    });

    it('should allow updating a contract', async () => {
      const contractRef = doc(managerDb, contractPath);
      await setDoc(contractRef, createContractData());
      await assertSucceeds(updateDoc(contractRef, { updatedAt: Timestamp.now() }));
    });

    it('should allow deleting a contract', async () => {
      const contractRef = doc(managerDb, contractPath);
      await setDoc(contractRef, createContractData());
      await assertSucceeds(deleteDoc(contractRef));
    });
  });

  describe('As Admin', () => {
    beforeEach(() => {
      const context = getAuthContext(adminId, 'admin');
      adminDb = context.firestore();
    });

    it('should allow creating a contract', async () => {
      const contractRef = doc(adminDb, contractPath);
      await assertSucceeds(setDoc(contractRef, createContractData()));
    });

    it('should allow reading a contract', async () => {
      const contractRef = doc(adminDb, contractPath);
      await setDoc(contractRef, createContractData());
      await assertSucceeds(getDoc(contractRef));
    });

    it('should allow updating a contract', async () => {
      const contractRef = doc(adminDb, contractPath);
      await setDoc(contractRef, createContractData());
      await assertSucceeds(updateDoc(contractRef, { updatedAt: Timestamp.now() }));
    });

    it('should allow deleting a contract', async () => {
      const contractRef = doc(adminDb, contractPath);
      await setDoc(contractRef, createContractData());
      await assertSucceeds(deleteDoc(contractRef));
    });
  });

  describe('As Member', () => {
    beforeEach(() => {
      const context = getAuthContext(memberId, 'member');
      memberDb = context.firestore();
    });

    it('should allow reading a contract', async () => {
      const contractRef = doc(memberDb, contractPath);
      await setDoc(doc(getAuthContext(managerId, 'manager').firestore(), contractPath), createContractData());
      await assertSucceeds(getDoc(contractRef));
    });

    it('should FAIL creating a contract', async () => {
      const contractRef = doc(memberDb, contractPath);
      await assertFails(setDoc(contractRef, createContractData()));
    });

    it('should FAIL updating a contract', async () => {
      const contractRef = doc(memberDb, contractPath);
      await setDoc(doc(getAuthContext(managerId, 'manager').firestore(), contractPath), createContractData());
      await assertFails(updateDoc(contractRef, { updatedAt: Timestamp.now() }));
    });

    it('should FAIL deleting a contract', async () => {
      const contractRef = doc(memberDb, contractPath);
      await setDoc(doc(getAuthContext(managerId, 'manager').firestore(), contractPath), createContractData());
      await assertFails(deleteDoc(contractRef));
    });
  });

  describe('As Unauthenticated User', () => {
    beforeEach(() => {
      unauthDb = testEnv.unauthenticatedContext().firestore();
    });

    it('should FAIL reading a contract', async () => {
      const contractRef = doc(unauthDb, contractPath);
      await assertFails(getDoc(contractRef));
    });

    it('should FAIL creating a contract', async () => {
      const contractRef = doc(unauthDb, contractPath);
      await assertFails(setDoc(contractRef, createContractData()));
    });

    it('should FAIL updating a contract', async () => {
      const contractRef = doc(unauthDb, contractPath);
      await assertFails(updateDoc(contractRef, { updatedAt: Timestamp.now() }));
    });

    it('should FAIL deleting a contract', async () => {
      const contractRef = doc(unauthDb, contractPath);
      await assertFails(deleteDoc(contractRef));
    });
  });

  describe('Cross-Organization Access for Contracts', () => {
    it('should FAIL to create a contract in another org', async () => {
      const context = getAuthContext(managerId, 'manager');
      const db = context.firestore();
      const invalidRef = doc(db, `organizations/${anotherOrganizationId}/contracts/${contractId}`);
      const invalidData = createContractData();
      invalidData.organizationId = anotherOrganizationId;
      await assertFails(setDoc(invalidRef, invalidData));
    });

    it('should FAIL to read a contract from another org', async () => {
      const otherContext = getAuthContext(managerId, 'manager', anotherOrganizationId);
      const otherDb = otherContext.firestore();
      const otherRef = doc(otherDb, contractPath);
      await assertFails(getDoc(otherRef));
    });
  });
});
