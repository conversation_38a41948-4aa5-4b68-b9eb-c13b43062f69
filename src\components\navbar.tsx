"use client";

import Link from "next/link";
import { Menu, Home, Building, Key } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { LoginDialog } from "./auth/login-dialog";
import { ThemeToggle } from "./theme-toggle";
import { useEffect, useState } from "react";
import Image from "next/image";
import { Logo } from "./logo";

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  function scrollToSection(
    e: React.MouseEvent<HTMLAnchorElement>,
    sectionId: string
  ) {
    e.preventDefault();

    // Check if we're on the homepage
    const isHomePage = window.location.pathname === "/";

    if (isHomePage) {
      const section = document.getElementById(sectionId);
      if (section) {
        const offset = 100;
        const elementPosition = section.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth",
        });
      }
    } else {
      // If not on homepage, navigate to homepage with hash
      window.location.href = `/#${sectionId}`;
    }
  }

  return (
    <div className="sticky top-0 z-50 w-full transition-all duration-200">
      <div 
        className={`w-full backdrop-blur-md py-2 transition-all duration-300 ${
          isScrolled 
            ? "bg-background/90 shadow-sm border-b border-border" 
            : "bg-transparent"
        }`}
      >
        <div className="flex h-16 items-center px-4 md:px-6 max-w-7xl mx-auto justify-between">
          {/* Logo */}
          <Logo />

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-8">
            <Link
              href="#features"
              onClick={(e) => scrollToSection(e, "features")}
              className="text-sm font-medium transition-colors hover:text-primary text-muted-foreground"
            >
              Funktionen
            </Link>
            <Link
              href="#how-it-works"
              onClick={(e) => scrollToSection(e, "how-it-works")}
              className="text-sm font-medium transition-colors hover:text-primary text-muted-foreground"
            >
              So funktioniert&apos;s
            </Link>
            <Link
              href="#preise"
              onClick={(e) => scrollToSection(e, "preise")}
              className="text-sm font-medium transition-colors hover:text-primary text-muted-foreground"
            >
              Preise
            </Link>
            <Link
              href="/immobilien"
              className="text-sm font-medium transition-colors hover:text-primary text-muted-foreground"
            >
              Immobilien
            </Link>
            <Link
              href="/blog"
              className="text-sm font-medium transition-colors hover:text-primary text-muted-foreground"
            >
              Blog
            </Link>
            <div className="flex items-center gap-2">
              <LoginDialog />
              <ThemeToggle />
            </div>
          </nav>

          {/* Mobile Navigation */}
          <div className="flex items-center gap-2 md:hidden">
            <ThemeToggle />
            <Sheet>
              <SheetTrigger asChild className="md:hidden">
                <Button variant="outline" size="icon" className="h-9 w-9">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Menü öffnen</span>
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>
                    <Logo />
                  </SheetTitle>
                </SheetHeader>
                <div className="flex flex-col items-start justify-center h-full gap-6 -mt-16">
                  <Link
                    href="#features"
                    onClick={(e) => scrollToSection(e, "features")}
                    className="text-base font-medium transition-colors hover:text-primary text-muted-foreground"
                  >
                    Funktionen
                  </Link>
                  <Link
                    href="#how-it-works"
                    onClick={(e) => scrollToSection(e, "how-it-works")}
                    className="text-base font-medium transition-colors hover:text-primary text-muted-foreground"
                  >
                    So funktioniert&apos;s
                  </Link>
                  <Link
                    href="#preise"
                    onClick={(e) => scrollToSection(e, "preise")}
                    className="text-base font-medium transition-colors hover:text-primary text-muted-foreground"
                  >
                    Preise
                  </Link>
                  <Link
                    href="/immobilien"
                    className="text-base font-medium transition-colors hover:text-primary text-muted-foreground"
                  >
                    Immobilien
                  </Link>
                  <Link
                    href="/blog"
                    className="text-base font-medium transition-colors hover:text-primary text-muted-foreground"
                  >
                    Blog
                  </Link>
                  <div className="mt-4">
                    <LoginDialog />
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </div>
  );
}
