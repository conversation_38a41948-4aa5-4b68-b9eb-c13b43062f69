# VermietOS.de
Art des Projekts: Immobilienverwaltungssoftware
Zielgruppe: Vermieter und Immobilienbesitzer
Kurzbeschreibung:
VermietOS ist eine umfassende Webanwendung, die speziell für Vermieter entwickelt wurde, um die Verwaltung von Immobilien, Mi<PERSON>n, Mietverträgen und Finanzen zu vereinfachen. Die App bietet ein zentrales Dashboard mit Echtzeit-Kennzahlen, detailliertes Finanz- und Transaktionsmanagement sowie Funktionen zur Überwachung von Auslastung und Mieteingängen. Mit einer benutzerfreundlichen Oberfläche und potenziellen Automatisierungsfunktionen unterstützt VermietOS Vermieter dabei, ihre Prozesse effizient zu gestalten und den Überblick über ihre Immobilien zu behalten.
Technologie:
Die Anwendung basiert auf Next.js mit TypeScript, nutzt Firebase für Datenbank- und Speicherfunktionen und integriert moderne UI-Komponenten wie Shadcn UI und Tailwind CSS für ein ansprechendes Design.
Kernfunktionen:
Immobilien- und Einheitenverwaltung
Mieter- und Mietvertragsmanagement
Finanz- und Transaktionsübersicht
Dashboard mit Echtzeit-Statistiken
Auslastungs- und Leerstandsmanagement
Dokumentenmanagement

// ========================================================================
// Firestore Collection Paths
// ========================================================================


organizations Collection:
/organizations/{orgId}

Users:
/usersProfiles/{userId}  (Contains UserProfile)

UserOrganizations (Link Table):
/userOrganizations/{userOrgId} (or use composite ID user_org)
  -> userId, organizationId, role

Invitations:
/invitations/{invitationId}

Objects Subcollection (under Property):
/organizations/{orgId}/objects/{objectId}

Units Subcollection (under Object):
/organizations/{orgId}/objects/{objectId}/units/{unitId}

Tenants Subcollection (under Organisation):
/organizations/{orgId}/tenants/{tenantId}

Contracts Subcollection (under Organisation):
/organizations/{orgId}/contracts/{contractId}

Transactions Subcollection (under Organisation):
/organizations/{orgId}/transactions/{transactionId}



// ========================================================================
// Example Usage & Relationships
// ========================================================================


1. An `Organization` (e.g., "Management Inc.") is the top-level entity.
2. A `Property` ("Main St. Complex", with address) belongs to an `Organization`.
   - Path: /organizations/{orgId}/properties/{propertyId}
3. An `Object` ("Building A") belongs to a `Property`.
   - Path: /organizations/{orgId}/properties/{propertyId}/objects/{objectId}
4. A `Unit` ("Apartment 101") belongs to an `Object`.
   - Path: /organizations/{orgId}/properties/{propertyId}/objects/{objectId}/units/{unitId}
5. A `Tenant` ("John Doe") belongs to an `Organization`.
   - Path: /organizations/{orgId}/tenants/{tenantId}
6. A `Contract` (`RentalAgreement`) links a `Tenant` to a `Unit` for a specific period.
   - It contains `tenantId` and `unitId`.
   - Path: /organizations/{orgId}/contracts/{contractId}
   - Querying: Find contracts by `tenantId` or `unitId`.
7. A `Transaction` records financial events (rent, expenses) related to the organization, potentially linked to properties, objects, units, tenants, or contracts.
   - Path: /organizations/{orgId}/transactions/{transactionId}
8. A `UserProfile` stores user details.
9. An `Invitation` allows users to join an `Organization`.
10. A `UserOrganization` links a `User` to an `Organization` with a specific role.


Every time you choose to apply a rule(s), explicitly state the rule(s) in the output. You can abbreviate the rule description to a single word or phrase.

-- Global Types which are used in different files are declared in the src/types/index.ts file.

## Code Style and Structure

- Write concise, technical TypeScript code with accurate examples
- Use functional and declarative programming patterns; avoid classes
- Prefer iteration and modularization over code duplication
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)
- Structure repository files as follows:

```
server/
├── src/
    ├── components/     # Shared React components
    ├── hooks/          # Custom React hooks
    ├── utils/          # Helper functions
    ├── types/          # TypeScript types
    └── lib/            # Shared libraries
extension/
├── src/
    ├── background/     # Service worker scripts
    ├── content/        # Content scripts
    ├── popup/          # Extension popup UI
    ├── options/        # Extension options page
    ├── components/     # Shared React components
    ├── hooks/          # Custom React hooks
    ├── utils/          # Helper functions
    ├── lib/            # Shared libraries
    ├── types/          # TypeScript types
    └── storage/        # Chrome storage utilities
shared/
├── src/
    ├── types/          # TypeScript types, only used for shared types between server and extension
    └── utils/          # Helper functions, only used for shared functions between server and extension
```

## Tech Stack

- React
- TypeScript
- Tailwind CSS
- Shadcn UI
- Next.js
- Firebase

## Naming Conventions

- Use lowercase with dashes for directories (e.g., components/form-wizard)
- Favor named exports for components and utilities
- Use PascalCase for component files (e.g., VisaForm.tsx)
- Use camelCase for utility files (e.g., formValidator.ts)

## TypeScript Usage

- Use TypeScript for all code; prefer interfaces over types
- Avoid enums; use const objects with 'as const' assertion
- Use functional components with TypeScript interfaces
- Define strict types for message passing between different parts of the extension
- Use absolute imports for all files @/...
- Avoid try/catch blocks unless there's good reason to translate or handle error in that abstraction
- Use explicit return types for all functions

## State Management

- Use React Context for global state when needed
- Implement proper cleanup in useEffect hooks

## Syntax and Formatting

- Use "function" keyword for pure functions
- Avoid unnecessary curly braces in conditionals
- Use declarative JSX
- Implement proper TypeScript discriminated unions for message types

## UI and Styling

- Think like an senior designer from Apple when you have to design UI.
- Use Shadcn UI and Radix for components
- use `npx shadcn@latest add <component-name>` to add new shadcn components
- Implement Tailwind CSS for styling
- Consider extension-specific constraints (popup dimensions, permissions)
- When adding new shadcn component, document the installation command

## Error Handling

- Implement proper error boundaries
- Log errors appropriately for debugging
- Provide user-friendly error messages
- Handle network failures gracefully


## Security

- Implement Content Security Policy
- Sanitize user inputs
- Handle sensitive data properly
- Implement proper CORS handling

