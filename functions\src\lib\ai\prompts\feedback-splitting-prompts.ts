/**
 * Creates a prompt to analyze and split feedback into individual items
 */
export function createFeedbackSplittingPrompt(feedbackText: string): string {
  return `
    You are an expert AI assistant specializing in analyzing user feedback for software development.
    Your task is to determine if the provided feedback contains multiple distinct points or requests,
    and if so, split them into separate, individual feedback items.

    FEEDBACK TO ANALYZE:
    "${feedbackText}"

    ANALYSIS INSTRUCTIONS:

    1. **Single vs. Multiple Point Analysis:**
       - **SINGLE POINT** (isMultiple: false): The feedback discusses one main feature, issue, or request, even if it mentions related aspects
       - **MULTIPLE POINTS** (isMultiple: true): The feedback contains 2 or more clearly distinct, independent requests that could be implemented separately

    2. **What Constitutes Multiple Points:**
       - Different UI elements or features
       - Separate functional requirements
       - Independent improvements or changes
       - Different areas of the application
       - Unrelated suggestions or requests

    3. **What Does NOT Constitute Multiple Points:**
       - Different aspects of the same feature
       - Related requirements for one functionality
       - Context or explanation for a single request
       - Examples or clarifications of one main point

    4. **Splitting Guidelines (if multiple points detected):**
       - Each feedback item MUST be self-contained and understandable WITHOUT the original context
       - CRITICAL: Include all necessary context in each split item (page/section/component references)
       - When referencing UI elements (buttons, forms, etc.), specify WHERE they are located
       - Preserve the original intent and add missing context for clarity
       - Make each item actionable as a separate development task
       - Use clear, descriptive titles for each item

    5. **Context Preservation Rules:**
       - If original feedback mentions "the button", specify WHICH button and WHERE
       - If original feedback mentions "the page", specify WHICH page or section
       - Include location context: "on the object creation page", "in the unit section", etc.
       - Add component/feature context: "floor selection button", "navigation menu", etc.
       - Ensure each split item can be understood by someone who hasn't read the original feedback

    6. **Output Format:**
       - If single point: Return isMultiple: false with one feedback item containing the original text
       - If multiple points: Return isMultiple: true with separate feedback items and a summary

    EXAMPLES:

    Single Point Example:
    Input: "The login button should be bigger and more prominent, maybe blue color and centered"
    Output: isMultiple: false (one feature - login button styling)

    Multiple Points Example:
    Input: "The login button should be blue, and we also need a forgot password link on the page"
    Output: isMultiple: true (two separate features - button styling + forgot password)

    Context Preservation Example:
    Input: "Beim Objekt erstellen, kann ich EG, 1Stock, etc. auswählen, Hochparterre und Souterrain wären da noch gut drin. Außerdem sollte der button zum auswählen blau sein und bei der Einheit Seite sollte noch ein Button hinzu um eine neue einheit anzulegen."

    WRONG Output:
    - "Außerdem sollte der button zum auswählen blau sein" (missing context - which button?)

    CORRECT Output:
    - "Der Auswahl-Button für die Stockwerk-Auswahl beim Objekt erstellen sollte blau sein" (includes context)

    IMPORTANT:
    - Be conservative - when in doubt, treat as single point
    - Each split item must be independently implementable
    - Preserve all important context in each split item
    - Provide clear, actionable titles for each item
  `;
}
