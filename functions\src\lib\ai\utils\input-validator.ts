import { HttpsError } from "firebase-functions/v2/https";

/**
 * Validates input data for profile generation
 */
export function validateProfileInput(data: any): void {
  if (!data.icpDescription || typeof data.icpDescription !== "string") {
    throw new HttpsError("invalid-argument", "ICP description is required");
  }
  
  if (data.productDescription && typeof data.productDescription !== "string") {
    throw new HttpsError("invalid-argument", "Product description must be a string");
  }
} 