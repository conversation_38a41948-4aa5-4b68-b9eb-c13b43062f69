"use client";

import { auth, db, functions } from "@/app/firebase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { analytics } from "@/app/firebase";
import { logEvent } from "firebase/analytics";
import {
  GithubAuthProvider,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  AuthError,
  sendPasswordResetEmail,
  User,
  updateProfile,
} from "firebase/auth";
import {
  doc,
  getDoc,
  setDoc,
  serverTimestamp,
  runTransaction,
  collection,
  addDoc,
  updateDoc,
  Timestamp,
} from "firebase/firestore";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Checkbox } from "@/components/ui/checkbox";
import { Eye, EyeOff } from "lucide-react";
import { analyticsService } from "@/lib/analytics/analyticsService";
import { httpsCallable } from "firebase/functions";
import { useAuth } from "@/components/providers/auth-provider";
interface SignInProps {
  defaultIsSignUp?: boolean;
}

export function SignIn({ defaultIsSignUp = false }: SignInProps) {
  const { refreshUserToken } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSignUp, setIsSignUp] = useState(defaultIsSignUp);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [organizationName, setOrganizationName] = useState("");
  const [hasAcceptedTerms, setHasAcceptedTerms] = useState(false);
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState("");
  const [isResetting, setIsResetting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordError, setPasswordError] = useState("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get("redirect") || "/app/dashboard";

  // Clear error messages when inputs change
  useEffect(() => {
    if (passwordError && password) {
      setPasswordError("");
    }
  }, [password, passwordError]);

  // Validate password strength
  const validatePassword = (password: string): boolean => {
    if (password.length < 6) {
      setPasswordError("Das Passwort muss mindestens 6 Zeichen lang sein");
      return false;
    }
    setPasswordError("");
    return true;
  };

  const signInWithEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading) return;

    setIsLoading(true);
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      // Update lastLogin on successful sign-in
      const userProfileRef = doc(db, "userProfiles", result.user.uid);
      await updateDoc(userProfileRef, { lastLogin: serverTimestamp() });
      toast.success("Willkommen zurück!");
      // Increase timeout to ensure toast is visible
      await new Promise((resolve) => setTimeout(resolve, 1000));
      router.push(redirectPath); // Use redirect path instead of hardcoded dashboard
    } catch (error: any) {
      const errorCode = error?.code || "auth/unknown";
      // Handle specific auth errors
      switch (errorCode) {
        case "auth/invalid-credential":
        case "auth/wrong-password":
        case "auth/user-not-found":
        case "auth/invalid-login-credentials":
          toast.error("Ungültige Email oder Passwort");
          break;
        case "auth/invalid-email":
          toast.error("Ungültige Email-Adresse");
          break;
        case "auth/user-disabled":
          toast.error("Dieser Account wurde deaktiviert");
          break;
        case "auth/too-many-requests":
          toast.error(
            "Zu viele Anmeldeversuche. Bitte warten Sie einen Moment"
          );
          break;
        default:
          toast.error(`Fehler bei der Anmeldung: ${errorCode}`);
          break;
      }
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGitHub = async () => {
    if (isLoading) return;
    setIsLoading(true);

    try {
      const provider = new GithubAuthProvider();
      provider.addScope("repo"); // Add repo scope to access repositories
      
      const result = await signInWithPopup(auth, provider);
      const user = result.user;
      const credential = GithubAuthProvider.credentialFromResult(result);
      const token = credential?.accessToken; // Save this token for GitHub API access

      if (!user || !user.email) {
        throw new Error("No user data or email received from GitHub");
      }

      const userProfileRef = doc(db, "userProfiles", user.uid);
      const userProfileSnap = await getDoc(userProfileRef);

      if (!userProfileSnap.exists()) {
        // New user via GitHub - Create Org and Profile directly
        try {
          // Skip validation step that's causing authentication issues
          // Create user and organization directly
          const displayName = user.displayName || "";
          const nameParts = displayName.split(" ");
          const userFirstName = nameParts[0] || "";
          const userLastName = nameParts.slice(1).join(" ") || "";

          const createUserAndOrg = httpsCallable(functions, "createUserAndOrganization");
          await createUserAndOrg({ 
            organizationName: `${displayName || "Neue"} Organisation`,
            firstName: userFirstName,
            lastName: userLastName,
            displayName,
            githubToken: token, // Save GitHub token
            email: user.email // Pass email for validation if needed
          });

          // Rest of your code remains the same
          await trackConversionEvent();
          await analyticsService.trackCustomEvent(
            "userRegistered",
            1,
            true,
            true
          );
          toast.success("Willkommen!");
        } catch (functionError: any) {
          // Handle errors from the createUserAndOrg function
          // If this function needs to validate the user, it can do it internally
          // with proper authentication
          console.error("GitHub Sign-Up Error:", functionError);
          
          // Clean up if needed
          try {
            await user.delete();
          } catch (deleteError) {
            console.error("Failed to delete user after error:", deleteError);
          }
          
          // Show appropriate error
          if (functionError.message?.includes("permission-denied")) {
            toast.error("Registrierung nicht möglich. Bitte kontaktieren Sie den Support.");
          } else {
            toast.error("Fehler bei der Registrierung. Bitte versuchen Sie es später erneut.");
          }
        }
      } else {
        // Existing user via GitHub - Update lastLogin and GitHub token in user profile directly
        await updateDoc(userProfileRef, { 
          lastLogin: serverTimestamp(),
          githubToken: token // Store token directly in user profile
        });
        toast.success("Willkommen zurück!");
      }

      // Refresh user token to include GitHub access
      await refreshUserToken();

      // Redirect on success (both new and existing)
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Ensure toast visibility
      router.push(redirectPath); // Use redirect path instead of hardcoded dashboard
    } catch (error: any) {
      // Handle GitHub Popup errors
      switch (error.code) {
        case "auth/popup-closed-by-user":
          // Optional: toast.info("Anmeldung abgebrochen"); - Can be noisy
          break;
        case "auth/popup-blocked":
          toast.error(
            "Popup wurde blockiert. Bitte erlauben Sie Popups für diese Seite"
          );
          break;
        case "auth/account-exists-with-different-credential":
          toast.error(
            "Ein Account mit dieser Email existiert bereits mit einer anderen Anmeldemethode. Bitte melden Sie sich stattdessen mit Email/Passwort an."
          );
          break;
        case "auth/unauthorized-domain":
          toast.error(
            "Diese Domain ist nicht für die GitHub-Anmeldung freigegeben. Bitte kontaktieren Sie den Support."
          );
          break;
        default:
          // Avoid toast for cancelled popups, log others
          if (error.code !== "auth/cancelled-popup-request") {
            console.error("GitHub Sign-In Error:", error);
            toast.error(`Fehler bei der Anmeldung: ${error.message || error.code}`);
          }
      }
    } finally {
      setIsLoading(false);
    }
  };

  async function trackConversionEvent() {
    const analyticsInstance = await analytics;
    if (analyticsInstance) {
      logEvent(analyticsInstance, "conversion_event_submit_lead_form", {
        currency: "EUR",
        value: 1.0,
        method: "form", // Or differentiate between google/email
      });
    }
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading) return;

    // --- Form Validation ---
    if (!hasAcceptedTerms) {
      toast.error(
        "Bitte akzeptieren Sie die Nutzungsbedingungen und Datenschutzerklärung"
      );
      return;
    }
    if (!firstName.trim() || !lastName.trim()) {
      toast.error("Bitte geben Sie Ihren Vor- und Nachnamen ein");
      return;
    }
    if (!organizationName.trim()) {
      toast.error("Bitte geben Sie den Namen Ihrer Organisation/Firma ein");
      return;
    }
    if (!validatePassword(password)) {
      return; // Error toast is handled by validatePassword
    }
    // --- End Validation ---

    setIsLoading(true);
    let createdUser: User | null = null; // Keep track of created user for potential cleanup

    try {
      // 1. Create Firebase Auth user
      const result = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      createdUser = result.user; // Store user in case later steps fail

      // --- Update Auth User Profile ---
      // Set the displayName on the Auth user object itself
      await updateProfile(createdUser, {
        displayName: `${firstName.trim()} ${lastName.trim()}`
      });
      // --- End Update Auth User Profile ---

      // 2. Call the Cloud Function to create organization, profile and userOrganization
      try {
        const createUserAndOrg = httpsCallable(functions, "createUserAndOrganization");
        await createUserAndOrg({
          organizationName,
          firstName: firstName.trim(),
          lastName: lastName.trim(),
          displayName: `${firstName.trim()} ${lastName.trim()}`
        });
        toast.success("Account erfolgreich erstellt! Willkommen.");
        
        // 3. Track conversion event
        await trackConversionEvent();
        await analyticsService.trackCustomEvent("userRegistered", 1, true, true);
        
        // 4. Show success and redirect
        router.push(redirectPath);
      } catch (functionError: any) {
        // Handle Cloud Function errors
        console.error("Error calling createUserAndOrganization:", functionError);
        
        // Clean up Auth user if Firestore/Function transaction failed
        if (createdUser) {
          try {
            await createdUser.delete();
            console.log("Cleaned up orphaned Auth user:", createdUser.uid);
          } catch (deleteError) {
            console.error("Failed to clean up orphaned Auth user:", deleteError);
          }
        }
        
        // Show appropriate error message
        if (functionError.message?.includes("permission-denied")) {
          await analyticsService.trackCustomEvent(
            "accountValidationFailed",
            1,
            false,
            false
          );
          toast.error(
            "Registrierung nicht möglich. Bitte kontaktieren Sie den Support."
          );
        } else {
          toast.error(`Fehler bei der Registrierung: ${functionError.message || "Unbekannter Fehler"}`);
        }
      }
    } catch (error: any) {
      console.error("Auth user creation error:", error);

      // Handle specific auth errors
      if (error.code === "auth/email-already-in-use") {
        toast.error(
          "Diese Email-Adresse wird bereits verwendet. Versuchen Sie sich anzumelden oder das Passwort zurückzusetzen."
        );
      } else if (error.code === "auth/weak-password") {
        // Should be caught by validatePassword, but handle defensively
        toast.error("Das Passwort muss mindestens 6 Zeichen lang sein");
      } else {
        // General error
        toast.error(`Fehler bei der Registrierung: ${error.message || error.code}`);
      }
      
      // Try to clean up the auth user if it was created
      if (createdUser) {
        try {
          await createdUser.delete();
        } catch (deleteError) {
          console.error("Failed to clean up auth user after error:", deleteError);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isResetting) return;

    if (!resetEmail.trim()) {
      toast.error("Bitte geben Sie Ihre Email-Adresse ein");
      return;
    }

    setIsResetting(true);
    try {
      // Check if user exists before sending reset email (optional, improves UX)
      // Note: This might reveal if an email is registered. Consider privacy implications.
      // try {
      //   await fetchSignInMethodsForEmail(auth, resetEmail); // Throws if not found
      // } catch (lookupError: any) {
      //    if (lookupError.code === 'auth/user-not-found') {
      //      toast.error("Kein Konto mit dieser E-Mail-Adresse gefunden");
      //      setIsResetting(false);
      //      return;
      //    }
      //    // Handle other lookup errors if necessary
      // }

      await sendPasswordResetEmail(auth, resetEmail, {
        // Configure your Action Code Settings URL correctly
        url: `${window.location.origin}/login?mode=resetPassword`, // Example URL
        handleCodeInApp: false, // Usually false for web password resets via link
      });
      toast.success(
        "E-Mail zum Zurücksetzen des Passworts wurde versendet (falls ein Konto existiert)."
      );
      setIsResetPassword(false);
      setResetEmail("");
    } catch (error: any) {
      console.error("Password reset error:", error);
      // Avoid specific "user-not-found" message here if not checking beforehand
      // to prevent email enumeration attacks.
      toast.error(
        "Fehler beim Versenden der E-Mail. Bitte versuchen Sie es erneut."
      );
    } finally {
      setIsResetting(false);
    }
  };

  // --- Render Logic ---
  return (
    <div className="w-full max-w-sm space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          {isResetPassword
            ? "Passwort zurücksetzen"
            : isSignUp
            ? "Konto erstellen (Vermieter)" // Clarify role
            : "Anmelden"}
        </h1>
        {!isResetPassword && !isSignUp && (
           <p className="text-sm text-muted-foreground">Melden Sie sich bei Ihrem Konto an.</p>
        )}
         {!isResetPassword && isSignUp && (
           <p className="text-sm text-muted-foreground">Erstellen Sie ein neues Vermieterkonto.</p>
        )}
        {isResetPassword && (
           <p className="text-sm text-muted-foreground">Geben Sie Ihre E-Mail ein, um Ihr Passwort zurückzusetzen.</p>
        )}
      </div>

      {!isResetPassword ? (
        <>
          {/* --- GitHub Sign-In Button & Disclaimer --- */}
          <div className="space-y-4">
            <Button
              variant="outline"
              className="w-full"
              onClick={signInWithGitHub}
              disabled={false}
              aria-label={`Mit GitHub ${isSignUp ? "registrieren" : "anmelden"}`}
            >
              {isLoading && !isSignUp ? ( // Show spinner only for sign-in action triggered here
                <div className="flex items-center justify-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
                  <span>Anmeldung läuft...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center gap-2">
                  {/* GitHub SVG Icon */}
                  <svg className="h-5 w-5" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12" />
                  </svg>
                  Mit GitHub {isSignUp ? "registrieren" : "anmelden"}
                </div>
              )}
            </Button>

            {/* Combined Terms/Privacy notice for GitHub */}
            <p className="text-xs text-muted-foreground px-4 text-center">
              Durch Klicken auf &quot;Mit GitHub...&quot; stimmen Sie unseren{" "}
              <Link href="/agb" className="text-primary hover:underline" target="_blank">
                AGB
              </Link>{" "}
              zu und bestätigen die Kenntnisnahme unserer{" "}
              <Link
                href="/datenschutz"
                className="text-primary hover:underline"
                 target="_blank"
              >
                Datenschutzerklärung
              </Link>.
            </p>

            {/* --- Divider --- */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Oder {isSignUp ? "registrieren" : "anmelden"} mit E-Mail
                </span>
              </div>
            </div>
          </div>

          {/* --- Email/Password Form --- */}
          <form
            onSubmit={isSignUp ? handleSignUp : signInWithEmail}
            className="space-y-4"
            noValidate // Prevent browser validation, rely on JS
          >
            {isSignUp && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                     <label htmlFor="firstName" className="sr-only">Vorname</label>
                     <Input
                       id="firstName"
                       placeholder="Vorname*"
                       value={firstName}
                       onChange={(e) => setFirstName(e.target.value)}
                       required
                       aria-required="true"
                     />
                  </div>
                  <div className="space-y-1">
                    <label htmlFor="lastName" className="sr-only">Nachname</label>
                    <Input
                      id="lastName"
                      placeholder="Nachname*"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      required
                      aria-required="true"
                    />
                  </div>
                </div>
                {/* Organization Name Input */}
                <div className="space-y-1">
                   <label htmlFor="organizationName" className="sr-only">Organisation/Firma</label>
                   <Input
                     id="organizationName"
                     placeholder="Organisation/Firma*"
                     value={organizationName}
                     onChange={(e) => setOrganizationName(e.target.value)}
                     required={isSignUp}
                     aria-required={isSignUp}
                   />
                </div>
              </>
            )}
            {/* Email Input */}
            <div className="space-y-1">
              <label htmlFor="email" className="sr-only">E-Mail-Adresse</label>
              <Input
                id="email"
                type="email"
                placeholder="E-Mail-Adresse*"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                aria-required="true"
                autoComplete="email"
              />
            </div>
            {/* Password Input */}
            <div className="relative space-y-1">
               <label htmlFor="password" className="sr-only">Passwort</label>
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Passwort*"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                aria-required="true"
                autoComplete={isSignUp ? "new-password" : "current-password"}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
                aria-label={showPassword ? "Passwort verbergen" : "Passwort anzeigen"}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </div>
            {passwordError && (
              <p className="text-sm text-red-500" role="alert">{passwordError}</p>
            )}

            {/* Terms Checkbox for Email Sign Up */}
            {isSignUp && (
              <div className="flex items-start space-x-2 pt-2">
                <Checkbox
                  id="terms"
                  required={isSignUp}
                  checked={hasAcceptedTerms}
                  onCheckedChange={(checked) =>
                    setHasAcceptedTerms(checked as boolean)
                  }
                  aria-required="true"
                />
                <label
                  htmlFor="terms"
                  className="text-sm text-muted-foreground leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Ich stimme den{" "}
                  <Link href="/agb" className="text-primary hover:underline" target="_blank">
                    Nutzungsbedingungen
                  </Link>{" "}
                  zu und bestätige die{" "}
                  <Link
                    href="/datenschutz"
                    className="text-primary hover:underline"
                    target="_blank"
                  >
                    Datenschutzerklärung
                  </Link>{" "}
                  gelesen zu haben.*
                </label>
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full rounded-full" // Consider removing rounded-full if inconsistent
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                   <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
                   <span>{isSignUp ? "Konto wird erstellt..." : "Anmeldung läuft..."}</span>
                </div>
              ) : isSignUp ? (
                "Konto erstellen"
              ) : (
                "Anmelden"
              )}
            </Button>
          </form>

          {/* --- Toggle Sign-Up/Sign-In & Forgot Password --- */}
          <div className="text-center text-sm flex flex-col gap-1 pt-2">
            {isSignUp ? (
              <div>
                <span>Bereits ein Konto? </span>
                <Button
                  onClick={() => {
                     setIsSignUp(false);
                     setPasswordError(""); // Clear potential errors when switching
                     // Clear form fields if desired
                     // setEmail(''); setPassword(''); setFirstName(''); setLastName(''); setOrganizationName(''); setHasAcceptedTerms(false);
                  }}
                  className="text-primary hover:underline p-0 h-auto"
                  variant="link"
                  type="button"
                >
                  Anmelden
                </Button>
              </div>
            ) : (
              <>
                <div>
                  <span>Noch kein Konto? </span>
                  <Button
                    onClick={() => {
                      setIsSignUp(true);
                      setPasswordError(""); // Clear potential errors when switching
                       // Clear form fields if desired
                      // setEmail(''); setPassword('');
                    }}
                    className="text-primary hover:underline p-0 h-auto"
                    variant="link"
                    type="button"
                  >
                    Registrieren
                  </Button>
                </div>
                <Button
                  onClick={() => {
                     setIsResetPassword(true);
                     setPasswordError(""); // Clear errors
                  }}
                  className="text-primary hover:underline p-0 h-auto"
                  variant="link"
                  type="button"
                >
                  Passwort vergessen?
                </Button>
              </>
            )}
          </div>
        </>
      ) : (
        /* --- Password Reset Form --- */
        <div className="space-y-4">
          <form onSubmit={handlePasswordReset} className="space-y-4" noValidate>
             <div className="space-y-1">
                <label htmlFor="resetEmail" className="sr-only">E-Mail-Adresse</label>
                <Input
                  id="resetEmail"
                  type="email"
                  placeholder="E-Mail-Adresse"
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  required
                  aria-required="true"
                  autoComplete="email"
                />
             </div>
            <Button
              type="submit"
              variant="outline" // Consider primary variant
              className="w-full"
              disabled={isResetting}
            >
              {isResetting ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
                  <span>E-Mail wird gesendet...</span>
                </div>
              ) : (
                "Passwort zurücksetzen E-Mail senden"
              )}
            </Button>
          </form>
          <div className="text-center">
             <Button
                onClick={() => setIsResetPassword(false)}
                className="text-primary hover:underline text-sm p-0 h-auto"
                variant="link"
                type="button"
              >
                Zurück zur Anmeldung
              </Button>
          </div>
        </div>
      )}
    </div>
  );
}
