/**
 * Central export file for all application types.
 * Imports and re-exports types from individual files for easier access.
 */

// Export common types
export * from "./common"; // Address, DocumentInfo
export * from "./base";

// Export core entity types
export * from "./userProfiles";
export * from "./organizations";
export * from "./invitations";

/**
 * Represents a user's membership in an organization from the 'userOrganizations' collection.
 * Establishes the many-to-many relationship between users and organizations.
 */
export interface UserOrganization {
  /** Reference to the user ID */
  userId: string;
  /** Reference to the organization ID */
  organizationId: string;
  /** User's role within this specific organization */
  role: string;
  /** When the user last accessed this organization */
  lastAccessed?: any; // Firestore timestamp
  /** When the user-organization relationship was created */
  createdAt?: any; // Firestore timestamp
}

// ========================================================================
// Firestore Collection Paths
// ========================================================================

/*
Organizations Collection:
/organizations/{orgId}

Users:
/userProfiles/{userId}  (Contains UserProfile)

UserOrganizations (Link Table):
/userOrganizations/{userOrgId}
  -> userId, organizationId, role

Invitations:
/invitations/{invitationId}

*/
