import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MeterDialog } from '@/components/MeterDialog';
import { addDoc, collection, getDocs, query, where, serverTimestamp } from 'firebase/firestore';
import { toast } from 'sonner';

// -- Polyfill for Pointer Events API in JSDOM --
if (typeof window !== 'undefined' && !window.PointerEvent) {
  // Use MouseEvent as a basic polyfill for PointerEvent in JSDOM
  // @ts-expect-error // Ignore TypeScript error for this polyfill
  window.PointerEvent = window.MouseEvent;
}

if (typeof Element.prototype.hasPointerCapture === 'undefined') {
  Element.prototype.hasPointerCapture = jest.fn();
}
if (typeof Element.prototype.releasePointerCapture === 'undefined') {
  Element.prototype.releasePointerCapture = jest.fn();
}

// Mock scrollIntoView for JSDOM
if (typeof Element.prototype.scrollIntoView === 'undefined') {
  Element.prototype.scrollIntoView = jest.fn();
}

// -- Mocks for Firestore --
jest.mock('firebase/firestore', () => ({
  collection: jest.fn((db, path) => ({ path: path })), // Return object with path
  query: jest.fn(),
  where: jest.fn(),
  getDocs: jest.fn(),
  addDoc: jest.fn(),
  serverTimestamp: jest.fn(),
  getFirestore: jest.fn(() => ({})),
}));

// -- Mock toast --
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// -- Mock Firebase config (db) --
jest.mock('@/app/firebase', () => ({ db: {} }));

describe('MeterDialog Integration', () => {
  beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });
  afterAll(() => {
    (console.error as jest.Mock).mockRestore();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    // getDocs returns empty for units and meters
    (getDocs as jest.Mock).mockResolvedValue({ docs: [] });
    // addDoc returns a fake doc ref
    (addDoc as jest.Mock).mockResolvedValue({ id: 'mock-meter-id' });
    // consistent timestamp
    (serverTimestamp as jest.Mock).mockReturnValue('mock-ts');
  });

  test('creates a new meter and shows success toast', async () => {
    const user = userEvent.setup();
    render(
      <MeterDialog
        organizationId="org1"
        objectId="obj1"
        userId="user1"
        buttonEl={<button>Open Meter</button>}
      />
    );

    // Open the dialog
    await user.click(screen.getByRole('button', { name: /Open Meter/i }));

    // Fill meter number
    const numberInput = await screen.findByPlaceholderText('SN-0001');
    await user.type(numberInput, 'SN-1234');

    // Select meter type via combobox trigger
    const meterTypeCombo = screen.getByRole('combobox');
    await user.click(meterTypeCombo);
    // Find the option by role and name, then click it
    const stromOption = await screen.findByRole('option', { name: /Strom/i });
    await user.click(stromOption);

    // Submit form
    await user.click(screen.getByRole('button', { name: /Zähler speichern/i }));

    // Expect addDoc to have been called once
    await waitFor(() => expect(addDoc).toHaveBeenCalledTimes(1));

    // Check correct args for addDoc
    expect(addDoc).toHaveBeenCalledWith(
      expect.objectContaining({ path: 'meters' }),
      expect.objectContaining({
        meterNumber: 'SN-1234',
        assignedLevel: 'Objekt',
        meterType: 'Strom',
        meterActive: true,
        isMainMeter: false, // Default value
        location: '', // Default value
        notes: '', // Default value
        createdByUserId: 'user1',
        organizationId: 'org1',
        objectId: 'obj1',
        createdAt: 'mock-ts',
        updatedAt: 'mock-ts',
      })
    );

    // Show success toast
    expect(toast.success).toHaveBeenCalledWith('Zähler erfolgreich hinzugefügt');
  });

  test('displays validation errors when required fields are missing', async () => {
    const user = userEvent.setup();
    render(
      <MeterDialog
        organizationId="org1"
        objectId="obj1"
        userId="user1"
        buttonEl={<button>Open Meter</button>}
      />
    );

    // Open the dialog
    await user.click(screen.getByRole('button', { name: /Open Meter/i }));

    // Submit form without filling required fields
    await user.click(screen.getByRole('button', { name: /Zähler speichern/i }));

    // Expect validation message for meterNumber
    expect(await screen.findByText(/Zählernummer wird benötigt/i)).toBeInTheDocument();
    // Ensure no document is added
    expect(addDoc).not.toHaveBeenCalled();
  });

  test('creates a new meter at unit level and shows success toast', async () => {
    const user = userEvent.setup();
    render(
      <MeterDialog
        organizationId="org1"
        objectId="obj1"
        userId="user1"
        unitId="unit1"
        defaultAssignedLevel="Einheit"
        buttonEl={<button>Open Meter</button>}
      />
    );

    // Open the dialog
    await user.click(screen.getByRole('button', { name: /Open Meter/i }));

    // Fill meter number
    const numberInput = await screen.findByPlaceholderText('SN-0001');
    await user.type(numberInput, 'SN-5678');

    // Select meter type
    const meterTypeCombo = screen.getByRole('combobox');
    await user.click(meterTypeCombo);
    const gasOption = await screen.findByRole('option', { name: /Gas/i });
    await user.click(gasOption);

    // Submit form
    await user.click(screen.getByRole('button', { name: /Zähler speichern/i }));

    // Expect addDoc to be called
    await waitFor(() => expect(addDoc).toHaveBeenCalledTimes(1));

    // Verify call arguments
    expect(addDoc).toHaveBeenCalledWith(
      expect.objectContaining({ path: 'meters' }),
      expect.objectContaining({
        meterNumber: 'SN-5678',
        assignedLevel: 'Einheit',
        meterType: 'Gas',
        meterActive: true,
        isMainMeter: false,
        location: '',
        notes: '',
        unitId: 'unit1',
        createdByUserId: 'user1',
        organizationId: 'org1',
        objectId: 'obj1',
        createdAt: 'mock-ts',
        updatedAt: 'mock-ts',
      })
    );

    // Show success toast
    expect(toast.success).toHaveBeenCalledWith('Zähler erfolgreich hinzugefügt');
  });

  test('checkboxes have correct default state', async () => {
    const user = userEvent.setup();
    render(
      <MeterDialog
        organizationId="org1"
        objectId="obj1"
        userId="user1"
        buttonEl={<button>Open Meter</button>}
      />
    );

    // Open the dialog
    await user.click(screen.getByRole('button', { name: /Open Meter/i }));

    // Check default state of checkboxes
    expect(screen.getByLabelText('Aktiver Zähler')).toBeChecked();
    expect(screen.getByLabelText('Hauptzähler')).not.toBeChecked();
  });

  test('shows error toast when creating meter fails', async () => {
    // Simulate addDoc failure
    (addDoc as jest.Mock).mockRejectedValueOnce(new Error('fail'));

    const user = userEvent.setup();
    render(
      <MeterDialog
        organizationId="org1"
        objectId="obj1"
        userId="user1"
        buttonEl={<button>Open Meter</button>}
      />
    );

    // Open the dialog
    await user.click(screen.getByRole('button', { name: /Open Meter/i }));

    // Fill required fields
    const numberInput = await screen.findByPlaceholderText('SN-0001');
    await user.type(numberInput, 'SN-9999');
    const meterTypeCombo = screen.getByRole('combobox');
    await user.click(meterTypeCombo);
    const stromOption = await screen.findByRole('option', { name: /Strom/i });
    await user.click(stromOption);

    // Submit form
    await user.click(screen.getByRole('button', { name: /Zähler speichern/i }));

    // Expect error toast
    await waitFor(() => expect(toast.error).toHaveBeenCalledWith('Fehler beim Erstellen des Zählers'));
  });

  test('shows error toast when loading data fails', async () => {
    // Simulate getDocs failure
    (getDocs as jest.Mock).mockRejectedValueOnce(new Error('load fail'));

    const user = userEvent.setup();
    render(
      <MeterDialog
        organizationId="org1"
        objectId="obj1"
        userId="user1"
        buttonEl={<button>Open Meter</button>}
      />
    );

    // Open the dialog to trigger data fetch
    await user.click(screen.getByRole('button', { name: /Open Meter/i }));

    // Expect error toast for data loading
    await waitFor(() => expect(toast.error).toHaveBeenCalledWith('Fehler beim Laden der Daten'));
  });

  test('creates a new meter with location, notes, and main meter flag', async () => {
    const user = userEvent.setup();
    render(
      <MeterDialog
        organizationId="org1"
        objectId="obj1"
        userId="user1"
        buttonEl={<button>Open Meter</button>}
      />
    );

    // Open the dialog
    await user.click(screen.getByRole('button', { name: /Open Meter/i }));

    // Fill meter number and type
    const numberInput = await screen.findByPlaceholderText('SN-0001');
    await user.type(numberInput, 'SN-7777');
    const meterTypeCombo = screen.getByRole('combobox');
    await user.click(meterTypeCombo);
    const heatingOption = await screen.findByRole('option', { name: /Heizung/i });
    await user.click(heatingOption);

    // Toggle Hauptzähler (main meter)
    await user.click(screen.getByLabelText('Hauptzähler'));

    // Fill optional fields
    const locationInput = screen.getByPlaceholderText('z.B. Keller, 1. Stock');
    await user.type(locationInput, 'Dachboden');
    const notesInput = screen.getByPlaceholderText('Zusätzliche Informationen zum Zähler');
    await user.type(notesInput, 'Extra notes');

    // Submit form
    await user.click(screen.getByRole('button', { name: /Zähler speichern/i }));

    // Expect addDoc called with custom fields
    await waitFor(() => expect(addDoc).toHaveBeenCalledTimes(1));
    expect(addDoc).toHaveBeenCalledWith(
      expect.objectContaining({ path: 'meters' }),
      expect.objectContaining({
        meterNumber: 'SN-7777',
        meterType: 'Heizung',
        isMainMeter: true,
        location: 'Dachboden',
        notes: 'Extra notes',
        meterActive: true,
        assignedLevel: 'Objekt',
        createdByUserId: 'user1',
        organizationId: 'org1',
        objectId: 'obj1',
        createdAt: 'mock-ts',
        updatedAt: 'mock-ts',
      })
    );

    // Show success toast
    expect(toast.success).toHaveBeenCalledWith('Zähler erfolgreich hinzugefügt');
  });
});
