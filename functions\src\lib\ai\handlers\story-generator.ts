import * as functions from "firebase-functions/v1";
import { logger } from "firebase-functions/v2";
import { ai, mainModel } from "../config/genkit-config";
import { UserStorySchema, UserStory } from "../schemas/story-schema";
import { createStoryPrompt } from "../prompts/story-prompts";

/**
 * Helper function to validate and debug schema validation issues
 */
function validateAndDebugSchema(rawData: any, requestId: string, userId: string): UserStory {
  try {
    // Attempt to parse with the schema
    const validatedData = UserStorySchema.parse(rawData);

    logger.debug("Schema validation successful", {
      requestId,
      userId,
      validatedFields: Object.keys(validatedData),
      isRejected: validatedData.isRejected,
    });

    return validatedData;
  } catch (validationError: any) {
    // Log detailed validation error information
    logger.error("Schema validation failed", {
      requestId,
      userId,
      validationError: validationError.message,
      validationIssues: validationError.issues || [],
      rawDataKeys: rawData ? Object.keys(rawData) : "null",
      rawDataTypes: rawData ? Object.entries(rawData).map(([key, value]) => ({ key, type: typeof value })) : "null",
      rawDataSample: rawData ? JSON.stringify(rawData, null, 2) : "null",
    });

    // Re-throw the validation error
    throw validationError;
  }
}

/**
 * Transforms user feedback into a structured user story
 */
// Instead of exporting the function directly, create it to be exported from index.ts
const generateUserStoryFunction = async (data: any, context: any) => {
  const requestId = Date.now().toString();

  logger.debug("Story generation started", {
    requestId,
    functionName: "generateUserStoryFunction",
  });

  try {
    // Authentication is automatically handled by onCall
    if (!context || !context.auth) {
      logger.warn("Authentication missing", { requestId });
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required"
      );
    }

    const userId = context.auth.uid;
    logger.debug("User authenticated", { requestId, userId });

    // Basic validation
    if (!data.feedbackText || typeof data.feedbackText !== "string") {
      logger.warn("Invalid feedback text", {
        requestId,
        userId,
        feedbackProvided: !!data.feedbackText,
      });
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Feedback text is required"
      );
    }

    const projectContext = data.projectContext || "";
    const issuesContext = data.issuesContext || "";

    logger.debug("Input data validated", {
      requestId,
      userId,
      feedbackLength: data.feedbackText.length,
      feedbackPreview:
        data.feedbackText.substring(0, 100) +
        (data.feedbackText.length > 100 ? "..." : ""),
      projectContextLength: projectContext.length,
      issuesContextLength: issuesContext.length,
    });

    logger.info("Generating user story from feedback", {
      requestId,
      userId,
      hasProjectContext: !!projectContext,
      hasIssuesContext: !!issuesContext,
    });

    logger.debug("Creating prompt", { requestId, userId });
    const prompt = createStoryPrompt(
      data.feedbackText,
      projectContext,
      issuesContext
    );
    logger.debug("Prompt created", {
      requestId,
      userId,
      promptLength:
        typeof prompt === "string" ? prompt.length : "complex-prompt-object",
      promptPreview: typeof prompt === "string" ? prompt.substring(0, 200) + (prompt.length > 200 ? "..." : "") : "complex-prompt-object",
    });

    // Debug: Log the full prompt for debugging
    logger.debug("Full prompt content", {
      requestId,
      userId,
      fullPrompt: prompt,
    });

    logger.debug("Calling AI model", {
      requestId,
      userId,
      model: mainModel || "unknown-model",
      temperature: 0.7,
      maxOutputTokens: 1024,
    });

    let rawOutput: any;
    try {
      const result = await ai.generate({
        model: mainModel,
        prompt,
        output: { schema: UserStorySchema },
        config: {
          temperature: 0.7,
          maxOutputTokens: 1024,
        },
      });

      rawOutput = result.output;

      // Debug: Log raw AI response before schema validation
      logger.debug("Raw AI response received", {
        requestId,
        userId,
        rawOutput: JSON.stringify(rawOutput, null, 2),
        outputType: typeof rawOutput,
        outputKeys: rawOutput ? Object.keys(rawOutput) : "null",
        outputStringified: rawOutput ? JSON.stringify(rawOutput) : "null",
      });

      if (!rawOutput) {
        throw new Error("AI model returned empty output");
      }

      // Use the validation helper to validate and debug schema issues
      const userStory = validateAndDebugSchema(rawOutput, requestId, userId);

      logger.debug("AI response received", {
        requestId,
        userId,
        responseReceived: !!rawOutput,
        storyTitle: userStory.title,
        storyLength: JSON.stringify(userStory).length,
        storyFields: Object.keys(userStory),
      });

      logger.info("User story generated successfully", {
        requestId,
        userId,
        storyTitle: userStory.title,
      });

      return rawOutput;
    } catch (aiError) {
      // Log AI-specific errors with more detail
      const aiErrorMessage = aiError instanceof Error ? aiError.message : String(aiError);
      logger.error("AI generation error in story generation", {
        requestId,
        userId: context.auth?.uid || "unknown",
        aiError: aiErrorMessage,
        aiErrorType: aiError instanceof Error ? aiError.constructor.name : "Unknown",
        aiErrorStack: aiError instanceof Error ? aiError.stack : undefined,
        rawOutput: rawOutput ? JSON.stringify(rawOutput, null, 2) : "null",
        promptLength: typeof prompt === "string" ? prompt.length : 0,
      });
      throw aiError;
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorType =
      error instanceof Error ? error.constructor.name : "Unknown";

    logger.debug("Exception details", {
      requestId,
      errorType,
      errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    logger.error("Error generating user story", {
      requestId,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    // onCall automatically handles error forwarding to the client
    if (error instanceof functions.https.HttpsError) {
      throw error;
    } else {
      throw new functions.https.HttpsError("internal", errorMessage);
    }
  }
};

// Export the handler function to be used in index.ts
export { generateUserStoryFunction };
