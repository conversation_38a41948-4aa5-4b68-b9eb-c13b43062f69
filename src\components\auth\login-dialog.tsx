"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from "@/components/ui/dialog";
import { SignIn } from "@/components/auth/sign-in";
import { useAuth } from "@/components/providers/auth-provider";
import { useRouter } from "next/navigation";
import { useState, useEffect, useCallback } from "react";

export function LoginDialog() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [showFallbackButton, setShowFallbackButton] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Prefetch the app route for faster navigation
  useEffect(() => {
    if (user) {
      router.prefetch("/app/dashboard"); // TODO
    }
  }, [user, router]);

  // Handle redirection with proper cleanup
  useEffect(() => {
    let redirectTimer: NodeJS.Timeout;
    let fallbackTimer: NodeJS.Timeout;

    if (user && isRedirecting) {
      redirectTimer = setTimeout(() => {
        router.push("/app/dashboard"); // TODO
      }, 1000);

      fallbackTimer = setTimeout(() => {
        setShowFallbackButton(true);
      }, 5000);
    }

    // Clean up timers when component unmounts or dependencies change
    return () => {
      clearTimeout(redirectTimer);
      clearTimeout(fallbackTimer);
    };
  }, [user, isRedirecting, router]);

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setShowFallbackButton(false);
      setIsRedirecting(false);
    }
  }, [isOpen]);

  // Memoize the handler to avoid recreating it on every render
  const handleAppClick = useCallback(() => {
    if (user) {
      setIsRedirecting(true);
      setShowFallbackButton(false);
      router.push("/app/dashboard"); // TODO
    }
  }, [user, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex flex-col items-center gap-4">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-violet-500" />
          <p className="text-sm text-gray-500">Wird geladen...</p>
        </div>
      </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="default"
          className="bg-gradient-to-r from-blue-600 to-teal-500 text-white font-medium rounded-full transition-all duration-300 ease-out hover:scale-[1.02] hover:shadow-lg shadow-md"
          onClick={handleAppClick}
        >
          Zur App
          <svg
            className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-0.5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogTitle className="text-lg font-semibold"></DialogTitle>
        {!user && <SignIn />}
        {user && (
          <div className="flex flex-col items-center gap-4 p-4">
            <div className="flex flex-col items-center gap-2">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-200 border-t-violet-500" />
              <p className="text-center text-sm text-gray-600">Du wirst weitergeleitet...</p>
            </div>
            {showFallbackButton && (
              <Button 
                onClick={handleAppClick}
                className="w-full mt-4"
              >
                Erneut versuchen
              </Button>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
