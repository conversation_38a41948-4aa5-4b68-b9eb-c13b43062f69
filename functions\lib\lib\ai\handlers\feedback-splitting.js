"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.splitFeedbackFunction = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const v2_1 = require("firebase-functions/v2");
const genkit_config_1 = require("../config/genkit-config");
const feedback_splitting_schema_1 = require("../schemas/feedback-splitting-schema");
const feedback_splitting_prompts_1 = require("../prompts/feedback-splitting-prompts");
/**
 * Analyzes feedback text and splits it into individual items if it contains multiple distinct points
 */
const splitFeedbackFunction = async (data, context) => {
    var _a, _b;
    const requestId = Date.now().toString();
    v2_1.logger.debug("Feedback splitting started", {
        requestId,
        functionName: "splitFeedbackFunction",
    });
    try {
        // Validate authentication
        if (!context || !context.auth) {
            v2_1.logger.warn("Unauthenticated request", { requestId });
            throw new functions.https.HttpsError("unauthenticated", "The function must be called while authenticated.");
        }
        const userId = context.auth.uid;
        // Validate input
        if (!data.feedbackText || typeof data.feedbackText !== "string") {
            v2_1.logger.warn("Invalid input data", { requestId, userId, data });
            throw new functions.https.HttpsError("invalid-argument", "feedbackText is required and must be a string.");
        }
        const feedbackText = data.feedbackText.trim();
        if (!feedbackText) {
            v2_1.logger.warn("Empty feedback text", { requestId, userId });
            throw new functions.https.HttpsError("invalid-argument", "feedbackText cannot be empty.");
        }
        v2_1.logger.debug("Input data validated", {
            requestId,
            userId,
            feedbackLength: feedbackText.length,
            feedbackPreview: feedbackText.substring(0, 100) + (feedbackText.length > 100 ? "..." : ""),
        });
        v2_1.logger.info("Analyzing feedback for splitting", {
            requestId,
            userId,
        });
        v2_1.logger.debug("Creating splitting prompt", { requestId, userId });
        const prompt = (0, feedback_splitting_prompts_1.createFeedbackSplittingPrompt)(feedbackText);
        v2_1.logger.debug("Prompt created", {
            requestId,
            userId,
            promptLength: prompt.length,
            promptPreview: prompt.substring(0, 200) + (prompt.length > 200 ? "..." : ""),
        });
        // Debug: Log the full prompt for debugging
        v2_1.logger.debug("Full prompt content", {
            requestId,
            userId,
            fullPrompt: prompt,
        });
        v2_1.logger.debug("Calling AI model for feedback splitting", {
            requestId,
            userId,
            model: genkit_config_1.mainModel || "unknown-model",
            temperature: 0.3,
            maxOutputTokens: 1024,
        });
        let rawOutput;
        try {
            const result = await genkit_config_1.ai.generate({
                model: genkit_config_1.mainModel,
                prompt,
                output: { schema: feedback_splitting_schema_1.FeedbackSplittingSchema },
                config: {
                    temperature: 0.3,
                    maxOutputTokens: 1024,
                },
            });
            rawOutput = result.output;
            // Debug: Log raw AI response before schema validation
            v2_1.logger.debug("Raw AI response received", {
                requestId,
                userId,
                rawOutput: JSON.stringify(rawOutput, null, 2),
                outputType: typeof rawOutput,
                outputKeys: rawOutput ? Object.keys(rawOutput) : "null",
            });
            if (!rawOutput) {
                throw new Error("AI model returned empty output");
            }
            const splittingResponse = rawOutput;
            v2_1.logger.debug("AI splitting response received", {
                requestId,
                userId,
                responseReceived: !!rawOutput,
                isMultiple: splittingResponse.isMultiple,
                itemsCount: splittingResponse.feedbackItems.length,
                itemTitles: splittingResponse.feedbackItems.map(item => item.title),
                totalLength: JSON.stringify(splittingResponse).length,
            });
            // Debug: Log each individual feedback item
            splittingResponse.feedbackItems.forEach((item, index) => {
                v2_1.logger.debug(`Feedback item ${index + 1}`, {
                    requestId,
                    userId,
                    itemIndex: index,
                    title: item.title,
                    text: item.text,
                    textLength: item.text.length,
                });
            });
            v2_1.logger.info("Feedback splitting completed successfully", {
                requestId,
                userId,
                isMultiple: splittingResponse.isMultiple,
                itemsCount: splittingResponse.feedbackItems.length,
            });
            return splittingResponse;
        }
        catch (aiError) {
            // Log AI-specific errors with more detail
            const aiErrorMessage = aiError instanceof Error ? aiError.message : String(aiError);
            v2_1.logger.error("AI generation error in feedback splitting", {
                requestId,
                userId: ((_a = context.auth) === null || _a === void 0 ? void 0 : _a.uid) || "unknown",
                aiError: aiErrorMessage,
                aiErrorType: aiError instanceof Error ? aiError.constructor.name : "Unknown",
                aiErrorStack: aiError instanceof Error ? aiError.stack : undefined,
                rawOutput: rawOutput ? JSON.stringify(rawOutput, null, 2) : "null",
                promptLength: (prompt === null || prompt === void 0 ? void 0 : prompt.length) || 0,
            });
            throw aiError;
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorType = error instanceof Error ? error.constructor.name : "Unknown";
        v2_1.logger.error("Error in feedback splitting", {
            requestId,
            userId: ((_b = context.auth) === null || _b === void 0 ? void 0 : _b.uid) || "unknown",
            error: errorMessage,
            errorType,
            stack: error instanceof Error ? error.stack : undefined,
        });
        // Re-throw HttpsError as-is, wrap others
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        throw new functions.https.HttpsError("internal", "An error occurred while analyzing the feedback.");
    }
};
exports.splitFeedbackFunction = splitFeedbackFunction;
//# sourceMappingURL=feedback-splitting.js.map