/**
 * Represents an invitation to join an organization from the 'invitations' collection.
 */
export interface Invitation {
  /** Email address of the invited user */
  email: string;
  /** Role assigned to the invited user */
  role: string;
  /** ID of the organization the user is invited to */
  organizationId: string;
  /** Name of the organization the user is invited to */
  organizationName: string;
  /** ID of the user who created the invitation */
  invitedBy: string;
  /** Email of the user who created the invitation */
  invitedByEmail: string;
  /** Current status of the invitation (pending, accepted, expired) */
  status: 'pending' | 'accepted' | 'expired';
  /** When the invitation was created */
  createdAt: any; // Firestore timestamp
  /** When the invitation expires */
  expiresAt: any; // Firestore timestamp
  /** ID of the user who accepted the invitation (if accepted) */
  acceptedBy?: string;
  /** When the invitation was accepted (if accepted) */
  acceptedAt?: any; // Firestore timestamp
}

/**
 * Data structure for the acceptInvitation cloud function
 */
export interface AcceptInvitationData {
  /** ID of the invitation to accept */
  invitationId: string;
} 