import { initializeTestEnvironment, assertSucceeds, assertFails, RulesTestEnvironment } from '@firebase/rules-unit-testing';
import { doc, setDoc, getDoc, deleteDoc, Timestamp } from 'firebase/firestore';
import { ref, uploadBytes, getBytes, deleteObject } from 'firebase/storage';
import * as fs from 'fs';

jest.setTimeout(30000);

let testEnv: RulesTestEnvironment;
const projectId = 'o3domo';

// Common test data constants
const organizationId = 'test-org-123';
const anotherOrganizationId = 'another-org-456';
const managerId = 'test-manager-abc';
const adminId = 'test-admin-xyz';
const memberId = 'test-member-def';
const tenantId = 'test-tenant-xyz';
const tenantPath = `organizations/${organizationId}/tenants/${tenantId}`;
const storagePath = `organizations/${organizationId}/tenants/${tenantId}/test-file.pdf`;

/**
 * Helper to create auth contexts with roles matching security rules.
 */
const getAuthContext = (uid: string, role: 'admin' | 'manager' | 'member', orgId: string = organizationId) => {
  return testEnv.authenticatedContext(uid, {
    activeOrganizationId: orgId,
    activeRole: role,
    organizations: { [orgId]: role }
  });
};

/**
 * Sample tenant data matching Firestore schema and rules requirements.
 */
const createTenantData = (creatorId: string) => ({
  salutation: 'Herr',
  firstName: 'Max',
  lastName: 'Mustermann',
  dateOfBirth: Timestamp.fromDate(new Date('1990-01-01')),
  contact: { email: '<EMAIL>', phone: '123456789' },
  address: {
    street: 'Teststraße',
    houseNumber: '1',
    zipCode: '12345',
    city: 'Teststadt',
    country: 'Deutschland',
    additionalInfo: null,
  },
  country: 'Deutschland',
  source: 'landlord',
  organizationId,
  createdAt: Timestamp.now(),
  updatedAt: Timestamp.now(),
});

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId,
    firestore: {
      host: 'localhost',
      port: 8080,
      rules: fs.readFileSync('firestore.rules', 'utf8'),
    },
    storage: {
      host: 'localhost',
      port: 9199,
      rules: fs.readFileSync('storage.rules', 'utf8'),
    },
  });
});

afterEach(async () => {
  await testEnv.clearFirestore();
});

afterAll(async () => {
  await testEnv.cleanup();
});

describe('Firebase Security Rules Tests', () => {
  describe('Firestore Rules for Tenants', () => {
    describe('As Manager', () => {
      let managerDb: any;
      beforeEach(() => {
        managerDb = getAuthContext(managerId, 'manager').firestore();
      });

      it('should allow creating a tenant', async () => {
        const tenantRef = doc(managerDb, tenantPath);
        await assertSucceeds(setDoc(tenantRef, createTenantData(managerId)));
      });

      it('should allow reading a tenant', async () => {
        const tenantRef = doc(managerDb, tenantPath);
        await setDoc(tenantRef, createTenantData(managerId));
        await assertSucceeds(getDoc(tenantRef));
      });

      it('should allow deleting a tenant', async () => {
        const tenantRef = doc(managerDb, tenantPath);
        await setDoc(tenantRef, createTenantData(managerId));
        await assertSucceeds(deleteDoc(tenantRef));
      });
    });

    describe('As Admin', () => {
      let adminDb: any;
      let managerDb: any;
      beforeEach(() => {
        adminDb = getAuthContext(adminId, 'admin').firestore();
        managerDb = getAuthContext(managerId, 'manager').firestore();
      });

      it('should allow creating a tenant', async () => {
        const tenantRef = doc(adminDb, tenantPath);
        await assertSucceeds(setDoc(tenantRef, createTenantData(adminId)));
      });

      it('should allow reading tenants created by others', async () => {
        const managerRef = doc(managerDb, tenantPath);
        await setDoc(managerRef, createTenantData(managerId));
        const adminRef = doc(adminDb, tenantPath);
        await assertSucceeds(getDoc(adminRef));
      });

      it('should allow deleting tenants created by others', async () => {
        const managerRef = doc(managerDb, tenantPath);
        await setDoc(managerRef, createTenantData(managerId));
        const adminRef = doc(adminDb, tenantPath);
        await assertSucceeds(deleteDoc(adminRef));
      });
    });

    describe('As Authenticated User with Insufficient Role (Member)', () => {
      let memberDb: any;
      let managerDb: any;
      beforeEach(() => {
        memberDb = getAuthContext(memberId, 'member').firestore();
        managerDb = getAuthContext(managerId, 'manager').firestore();
      });

      it('should FAIL to create a tenant', async () => {
        const memberRef = doc(memberDb, tenantPath);
        await assertFails(setDoc(memberRef, createTenantData(memberId)));
      });

      it('should ALLOW reading a tenant', async () => {
        const managerRef = doc(managerDb, tenantPath);
        await setDoc(managerRef, createTenantData(managerId));
        const memberRef = doc(memberDb, tenantPath);
        await assertSucceeds(getDoc(memberRef));
      });

      it('should FAIL to delete a tenant', async () => {
        const managerRef = doc(managerDb, tenantPath);
        await setDoc(managerRef, createTenantData(managerId));
        const memberRef = doc(memberDb, tenantPath);
        await assertFails(deleteDoc(memberRef));
      });
    });

    describe('As Unauthenticated User', () => {
      let unauthDb: any;
      let managerDb: any;
      beforeEach(() => {
        unauthDb = testEnv.unauthenticatedContext().firestore();
        managerDb = getAuthContext(managerId, 'manager').firestore();
      });

      it('should FAIL to create a tenant', async () => {
        const refUnauth = doc(unauthDb, tenantPath);
        await assertFails(setDoc(refUnauth, createTenantData(managerId)));
      });

      it('should FAIL to read a tenant', async () => {
        const managerRef = doc(managerDb, tenantPath);
        await setDoc(managerRef, createTenantData(managerId));
        const unauthRef = doc(unauthDb, tenantPath);
        await assertFails(getDoc(unauthRef));
      });

      it('should FAIL to delete a tenant', async () => {
        const managerRef = doc(managerDb, tenantPath);
        await setDoc(managerRef, createTenantData(managerId));
        const unauthRef = doc(unauthDb, tenantPath);
        await assertFails(deleteDoc(unauthRef));
      });
    });

    describe('Cross-Organization Access & Invalid Data', () => {
      let managerDbAnother: any;
      let managerDbCurrent: any;
      beforeEach(() => {
        managerDbAnother = getAuthContext(managerId, 'manager', anotherOrganizationId).firestore();
        managerDbCurrent = getAuthContext(managerId, 'manager').firestore();
      });

      it('should FAIL to create a tenant in another org', async () => {
        const wrongPath = `organizations/${anotherOrganizationId}/tenants/${tenantId}`;
        const refOther = doc(managerDbCurrent, wrongPath);
        await assertFails(setDoc(refOther, createTenantData(managerId)));
      });

      it('should FAIL to create a tenant with incorrect organizationId field', async () => {
        const tenantRef = doc(managerDbCurrent, tenantPath);
        const badData = { ...createTenantData(managerId), organizationId: anotherOrganizationId };
        await assertFails(setDoc(tenantRef, badData));
      });
    });
  });

  describe('Storage Rules for Tenant Files', () => {
    describe('As Manager', () => {
      let managerDb: any;
      let managerStorage: any;
      const fileContent = Buffer.from('Test content');

      beforeEach(async () => {
        const managerContext = getAuthContext(managerId, 'manager');
        managerDb = managerContext.firestore();
        managerStorage = managerContext.storage();
        // Ensure tenant record exists
        await setDoc(doc(managerDb, tenantPath), createTenantData(managerId));
      });

      it('should allow uploading a file', async () => {
        const storageRef = ref(managerStorage, storagePath);
        await assertSucceeds(uploadBytes(storageRef, fileContent));
      });

      it('should allow reading an uploaded file', async () => {
        const storageRef = ref(managerStorage, storagePath);
        await uploadBytes(storageRef, fileContent);
        await assertSucceeds(getBytes(storageRef));
      });

      it('should allow deleting a file', async () => {
        const storageRef = ref(managerStorage, storagePath);
        await uploadBytes(storageRef, fileContent);
        await assertSucceeds(deleteObject(storageRef));
      });

      it('should FAIL to upload a file exceeding size limit', async () => {
        const largeContent = Buffer.alloc(25 * 1024 * 1024); // 25MB
        const largeRef = ref(managerStorage, storagePath.replace('test-file.pdf', 'large-file.bin'));
        await assertFails(uploadBytes(largeRef, largeContent));
      });
    });

    describe('As Admin', () => {
      let adminDb: any;
      let adminStorage: any;
      let managerDb: any;
      let managerStorage: any;
      const fileContent = Buffer.from('Test content');

      beforeEach(async () => {
        const adminContext = getAuthContext(adminId, 'admin');
        adminDb = adminContext.firestore();
        adminStorage = adminContext.storage();

        const managerContext = getAuthContext(managerId, 'manager');
        managerDb = managerContext.firestore();
        managerStorage = managerContext.storage();

        // Setup initial tenant and file
        await setDoc(doc(managerDb, tenantPath), createTenantData(managerId));
        await uploadBytes(ref(managerStorage, storagePath), fileContent);
      });

      it('should allow reading a file uploaded by others', async () => {
        await assertSucceeds(getBytes(ref(adminStorage, storagePath)));
      });

      it('should allow uploading a file', async () => {
        const newPath = storagePath.replace('test-file.pdf', 'admin-file.pdf');
        await setDoc(doc(adminDb, tenantPath), createTenantData(adminId));
        const storageRef = ref(adminStorage, newPath);
        await assertSucceeds(uploadBytes(storageRef, fileContent));
      });

      it('should allow deleting a file uploaded by others', async () => {
        await assertSucceeds(deleteObject(ref(adminStorage, storagePath)));
      });
    });

    describe('As Authenticated User with Insufficient Role (Member)', () => {
      let memberDb: any;
      let memberStorage: any;
      let managerDb: any;
      let managerStorage: any;
      const fileContent = Buffer.from('Test content');

      beforeEach(async () => {
        const memberContext = getAuthContext(memberId, 'member');
        memberDb = memberContext.firestore();
        memberStorage = memberContext.storage();

        const managerContext = getAuthContext(managerId, 'manager');
        managerDb = managerContext.firestore();
        managerStorage = managerContext.storage();

        await setDoc(doc(managerDb, tenantPath), createTenantData(managerId));
        await uploadBytes(ref(managerStorage, storagePath), fileContent);
      });

      it('should FAIL to upload a file', async () => {
        const storageRef = ref(memberStorage, storagePath.replace('test-file.pdf', 'member-file.pdf'));
        await assertFails(uploadBytes(storageRef, fileContent));
      });

      it('should ALLOW reading a file', async () => {
        await assertSucceeds(getBytes(ref(memberStorage, storagePath)));
      });

      it('should FAIL to delete a file', async () => {
        await assertFails(deleteObject(ref(memberStorage, storagePath)));
      });
    });

    describe('As Unauthenticated User', () => {
      let unauthStorage: any;
      let managerDb: any;
      let managerStorage: any;
      const fileContent = Buffer.from('Test content');

      beforeEach(async () => {
        unauthStorage = testEnv.unauthenticatedContext().storage();

        const managerContext = getAuthContext(managerId, 'manager');
        managerDb = managerContext.firestore();
        managerStorage = managerContext.storage();

        await setDoc(doc(managerDb, tenantPath), createTenantData(managerId));
        await uploadBytes(ref(managerStorage, storagePath), fileContent);
      });

      it('should FAIL to upload a file', async () => {
        const storageRef = ref(unauthStorage, storagePath);
        await assertFails(uploadBytes(storageRef, fileContent));
      });

      it('should FAIL to read a file', async () => {
        await assertFails(getBytes(ref(unauthStorage, storagePath)));
      });

      it('should FAIL to delete a file', async () => {
        await assertFails(deleteObject(ref(unauthStorage, storagePath)));
      });
    });
  });
});
