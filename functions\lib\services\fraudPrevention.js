"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FraudPreventionService = void 0;
const crypto = __importStar(require("crypto"));
const admin = __importStar(require("firebase-admin"));
const index_1 = require("../index");
const firestore_1 = require("firebase-admin/firestore");
/**
 * FraudPreventionService - GDPR-compliant account abuse prevention
 * Uses pseudonymized tokens to prevent abuse without storing personal data
 * Simplified to use only email address for maximum GDPR compliance
 */
class FraudPreventionService {
    constructor() {
        this.COLLECTION = 'accountTokens';
        this.TOKEN_EXPIRY_DAYS = 180; // 6 months retention
        this.db = admin.firestore();
    }
    /**
     * Generates a pseudonymized hash token based on email
     * The token cannot be reversed to identify a specific user
     */
    generateToken(email) {
        // Normalize email by converting to lowercase
        const normalizedEmail = email.toLowerCase().trim();
        // Create a hash that cannot be reversed but will be consistent for the same input
        // Using SHA-256 for strong cryptographic hashing
        return crypto.createHash('sha256').update(normalizedEmail).digest('hex');
    }
    /**
     * Stores token with expiration date when a user deletes their account
     * GDPR compliant as we don't store PII, just a hash that can't identify the user
     */
    async storeDeletedAccountToken(email) {
        const token = this.generateToken(email);
        // Set expiration date (6 months from now)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + this.TOKEN_EXPIRY_DAYS);
        // Store the token with expiration date
        await this.db.collection(this.COLLECTION).doc(token).set({
            token,
            createdAt: (0, index_1.serverTimestamp)(),
            expiresAt: firestore_1.Timestamp.fromDate(expiresAt),
        });
    }
    /**
     * Checks if a token exists for the given email
     * Returns true if a matching token is found (indicating potential abuse)
     */
    async checkTokenExists(email) {
        const token = this.generateToken(email);
        // Check if token exists in database
        const doc = await this.db.collection(this.COLLECTION).doc(token).get();
        return doc.exists;
    }
    /**
     * Delete expired tokens (scheduled task)
     * This ensures we don't store tokens longer than necessary (GDPR compliance)
     */
    async purgeExpiredTokens() {
        const now = firestore_1.Timestamp.now();
        const snapshot = await this.db.collection(this.COLLECTION)
            .where('expiresAt', '<', now)
            .get();
        const batch = this.db.batch();
        let count = 0;
        snapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
            count++;
        });
        if (count > 0) {
            await batch.commit();
        }
        return count;
    }
}
exports.FraudPreventionService = FraudPreventionService;
//# sourceMappingURL=fraudPrevention.js.map