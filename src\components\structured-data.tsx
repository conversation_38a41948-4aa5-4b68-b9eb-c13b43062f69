import React, { JSX } from 'react'

interface StructuredDataProps {
  type?: 'Organization' | 'WebSite' | 'WebPage'
}

export function StructuredData({ type = 'WebSite' }: StructuredDataProps): JSX.Element {
  // Basis-Daten für die Organisation
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'vermietOS',
    url: 'https://vermietos.de',
    logo: 'https://vermietos.de/logo.png',
    description: 'Smarte Immobilienverwaltung für möblierte Vermietung',
    sameAs: [
      'https://linkedin.com/company/vermietos'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '',
      contactType: 'customer service',
      email: '<EMAIL>',
      availableLanguage: 'German'
    }
  }

  // Daten für die Website
  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'vermiet<PERSON>',
    url: 'https://vermietos.de',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://vermietos.de/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  }

  // Daten für die aktuelle Seite
  const webpageData = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'vermietOS - Smarte Immobilienverwaltung',
    description: 'vermietOS ist Ihre Lösung für die smarte Verwaltung von Immobilien, spezialisiert auf möblierte Vermietung.',
    url: 'https://vermietos.de',
    isPartOf: {
      '@type': 'WebSite',
      name: 'vermietOS',
      url: 'https://vermietos.de'
    }
  }

  // Wähle die richtigen Daten basierend auf dem Typ
  const structuredData = 
    type === 'Organization' ? organizationData :
    type === 'WebPage' ? webpageData :
    websiteData

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
} 