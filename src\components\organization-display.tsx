"use client";

import * as React from "react";
import { ChevronsUpDown } from "lucide-react"; // Keep for potential future dropdown
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export function OrganizationDisplay({
  organizationName,
  plan,
  iconSrc,
}: {
  organizationName: string | null | undefined;
  plan: string;
  iconSrc: string;
}) {
  const name = organizationName || "Organisation";
  const fallback = name?.substring(0, 2).toUpperCase() || "O";

  // Using SidebarMenu structure like TeamSwitcher for consistency
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        {/* Render a div instead of SidebarMenuButton for non-interactive display */}
        <div 
          className="flex h-11 w-full items-center justify-start gap-2 rounded-md px-3 text-sm font-medium cursor-default" // Mimic button styles (height, padding, font, etc.)
        >
          <Avatar className="h-8 w-8 rounded-lg">
            <AvatarImage src={iconSrc} alt={name} />
            <AvatarFallback className="rounded-lg">{fallback}</AvatarFallback>
          </Avatar>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">{name}</span>
            <span className="truncate text-xs text-muted-foreground">{plan}</span>
          </div>
          {/* Omit ChevronsUpDown for now as it's not a dropdown */}
          {/* <ChevronsUpDown className="ml-auto size-4" /> */}
        </div>
      </SidebarMenuItem>
    </SidebarMenu>
  );
} 