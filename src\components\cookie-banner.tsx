"use client";
import Link from "next/link";
import { <PERSON><PERSON> } from "lucide-react";
import { useEffect, useState } from "react";

// Custom Hook für die Verwaltung von localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(
        `Fehler beim Lesen des localStorage-Schlüssels "${key}":`,
        error
      );
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(
        `<PERSON><PERSON> beim <PERSON> des localStorage-Schlüssels "${key}":`,
        error
      );
    }
  };

  return [storedValue, setValue] as const;
}

export function CookieBanner() {
  const [cookieConsent, setCookieConsent] = useLocalStorage<boolean | null>(
    "cookie_consent",
    null
  );
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (cookieConsent === null) {
        setIsVisible(true);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [cookieConsent]);

  const handleConsent = (consent: boolean) => {
    setCookieConsent(consent);
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div
      className="z-50 mb-2 mx-auto max-w-max sm:max-w-sm lg:max-w-xl fixed bottom-0 left-2 right-2 sm:right-auto
                    flex px-3 md:px-4 py-3 lg:py-4 justify-between items-center flex-col sm:flex-row gap-4  
                    bg-white rounded-xl border border-black shadow-md"
    >
      <div className="flex flex-col items-center gap-2 lg:flex-row lg:gap-5">
        <div>
          <Cookie className="text-gray-600 mt-5 lg:mt-0" />
        </div>
        <div className="text-center text-gray-700 text-sm lg:text-left w-fit">
          <p>
            Durch Klicken auf „Akzeptieren“ stimmen Sie der Speicherung von
            Cookies zu, um Ihre Nutzung der Website zu verbessern und
            personalisierte Inhalte anzubieten.{" "}
          </p>
        </div>

        <div className="flex gap-2 mt-3 lg:text-sm lg:flex-1 lg:mt-0">
          <button
            className="px-5 py-2 text-gray-600 rounded-md border border-gray-300 hover:bg-gray-100 transition-colors cursor-pointer"
            onClick={() => handleConsent(false)}
          >
            Ablehnen
          </button>
          <button
            className="px-5 py-2 text-white bg-black rounded-md hover:bg-neutral-700 transition-colors cursor-pointer"
            onClick={() => handleConsent(true)}
          >
            Akzeptieren
          </button>
        </div>
      </div>
    </div>
  );
}
