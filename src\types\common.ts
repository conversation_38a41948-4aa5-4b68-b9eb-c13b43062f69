import { UserProfile } from "./userProfiles"; // Import UserProfile for DocumentInfo

/**
 * Represents a generic address.
 */
export interface Address {
  street: string;
  houseNumber: string;
  zipCode: string;
  city: string;
  country: string; // Optional, defaults to a standard value if needed
  additionalInfo?: string | null; // e.g., "Building B", "c/o"
}

/**
 * Represents document information embedded in other types.
 */
export interface DocumentInfo {
  name: string; // Display name, e.g., "Handover Protocol 2023-01-15"
  url: string; // URL to the file in Cloud Storage
  type: string; // e.g., 'handover', 'invoice', 'notice'
  uploadedAt?: Date;
  uploadedByUserId?: UserProfile["id"];
}

/**
 * Represents a note/comment that can be attached to various entities.
 */
export interface Note {
  id: string; // Unique identifier for the note
  content: string; // The actual note content
  createdAt: Date; // When the note was created
  createdByUserId: UserProfile["id"]; // User who created the note
  createdByUserName?: string; // Display name of the user who created the note (for denormalization)
  updatedAt?: Date; // When the note was last updated (if applicable)
} 