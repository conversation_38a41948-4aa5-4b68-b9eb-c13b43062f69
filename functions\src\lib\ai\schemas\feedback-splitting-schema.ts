import { z } from "genkit";

// Schema for individual feedback item
const FeedbackItem = z.object({
  text: z.string().describe("Individual feedback text that represents a single, distinct point or request"),
  title: z.string().describe("Brief descriptive title for this feedback item"),
});

// Schema for feedback splitting response
export const FeedbackSplittingSchema = z.object({
  isMultiple: z.boolean().describe("Whether the original feedback contained multiple distinct points"),
  feedbackItems: z.array(FeedbackItem).min(1).describe("Array of individual feedback items"),
  summary: z.string().optional().describe("Brief explanation of how the feedback was split (only if isMultiple is true)"),
});

// Type definitions
export type FeedbackItem = z.infer<typeof FeedbackItem>;
export type FeedbackSplittingResponse = z.infer<typeof FeedbackSplittingSchema>;
