{"version": 3, "file": "story-generator.js", "sourceRoot": "", "sources": ["../../../../src/lib/ai/handlers/story-generator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iEAAmD;AACnD,8CAA+C;AAC/C,2DAAwD;AACxD,0DAAqE;AACrE,4DAA6D;AAE7D;;GAEG;AACH,SAAS,sBAAsB,CAAC,OAAY,EAAE,SAAiB,EAAE,MAAc;IAC7E,IAAI;QACF,mCAAmC;QACnC,MAAM,aAAa,GAAG,8BAAe,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAErD,WAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;YAC3C,SAAS;YACT,MAAM;YACN,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;YAC3C,UAAU,EAAE,aAAa,CAAC,UAAU;SACrC,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC;KACtB;IAAC,OAAO,eAAoB,EAAE;QAC7B,4CAA4C;QAC5C,WAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,SAAS;YACT,MAAM;YACN,eAAe,EAAE,eAAe,CAAC,OAAO;YACxC,gBAAgB,EAAE,eAAe,CAAC,MAAM,IAAI,EAAE;YAC9C,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;YACpD,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;YAC7G,aAAa,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;SACnE,CAAC,CAAC;QAEH,gCAAgC;QAChC,MAAM,eAAe,CAAC;KACvB;AACH,CAAC;AAED;;GAEG;AACH,qFAAqF;AACrF,MAAM,yBAAyB,GAAG,KAAK,EAAE,IAAS,EAAE,OAAY,EAAE,EAAE;;IAClE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAExC,WAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;QACvC,SAAS;QACT,YAAY,EAAE,2BAA2B;KAC1C,CAAC,CAAC;IAEH,IAAI;QACF,oDAAoD;QACpD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YAC7B,WAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YACrD,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,iBAAiB,EACjB,yBAAyB,CAC1B,CAAC;SACH;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;QAChC,WAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;QAE1D,mBAAmB;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE;YAC/D,WAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACnC,SAAS;gBACT,MAAM;gBACN,gBAAgB,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY;aACtC,CAAC,CAAC;YACH,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAClC,kBAAkB,EAClB,2BAA2B,CAC5B,CAAC;SACH;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE,CAAC;QAE/C,WAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;YACnC,SAAS;YACT,MAAM;YACN,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YACxC,eAAe,EACb,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBACnC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YAC/C,oBAAoB,EAAE,cAAc,CAAC,MAAM;YAC3C,mBAAmB,EAAE,aAAa,CAAC,MAAM;SAC1C,CAAC,CAAC;QAEH,WAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;YACjD,SAAS;YACT,MAAM;YACN,iBAAiB,EAAE,CAAC,CAAC,cAAc;YACnC,gBAAgB,EAAE,CAAC,CAAC,aAAa;SAClC,CAAC,CAAC;QAEH,WAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC;QACvD,MAAM,MAAM,GAAG,IAAA,iCAAiB,EAC9B,IAAI,CAAC,YAAY,EACjB,cAAc,EACd,aAAa,CACd,CAAC;QACF,WAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;YAC7B,SAAS;YACT,MAAM;YACN,YAAY,EACV,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,uBAAuB;YACtE,aAAa,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,uBAAuB;SACpI,CAAC,CAAC;QAEH,2CAA2C;QAC3C,WAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;YAClC,SAAS;YACT,MAAM;YACN,UAAU,EAAE,MAAM;SACnB,CAAC,CAAC;QAEH,WAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAC/B,SAAS;YACT,MAAM;YACN,KAAK,EAAE,yBAAS,IAAI,eAAe;YACnC,WAAW,EAAE,GAAG;YAChB,eAAe,EAAE,IAAI;SACtB,CAAC,CAAC;QAEH,IAAI,SAAc,CAAC;QACnB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC;gBAC/B,KAAK,EAAE,yBAAS;gBAChB,MAAM;gBACN,MAAM,EAAE,EAAE,MAAM,EAAE,8BAAe,EAAE;gBACnC,MAAM,EAAE;oBACN,WAAW,EAAE,GAAG;oBAChB,eAAe,EAAE,IAAI;iBACtB;aACF,CAAC,CAAC;YAEH,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;YAE1B,sDAAsD;YACtD,WAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,SAAS;gBACT,MAAM;gBACN,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7C,UAAU,EAAE,OAAO,SAAS;gBAC5B,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM;gBACvD,iBAAiB,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM;aAClE,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;aACnD;YAED,gEAAgE;YAChE,MAAM,SAAS,GAAG,sBAAsB,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAEvE,WAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACnC,SAAS;gBACT,MAAM;gBACN,gBAAgB,EAAE,CAAC,CAAC,SAAS;gBAC7B,UAAU,EAAE,SAAS,CAAC,KAAK;gBAC3B,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM;gBAC7C,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;aACpC,CAAC,CAAC;YAEH,WAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC/C,SAAS;gBACT,MAAM;gBACN,UAAU,EAAE,SAAS,CAAC,KAAK;aAC5B,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;SAClB;QAAC,OAAO,OAAO,EAAE;YAChB,0CAA0C;YAC1C,MAAM,cAAc,GAAG,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACpF,WAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE;gBACtD,SAAS;gBACT,MAAM,EAAE,CAAA,MAAA,OAAO,CAAC,IAAI,0CAAE,GAAG,KAAI,SAAS;gBACtC,OAAO,EAAE,cAAc;gBACvB,WAAW,EAAE,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;gBAC5E,YAAY,EAAE,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBAClE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;gBAClE,YAAY,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC7D,CAAC,CAAC;YACH,MAAM,OAAO,CAAC;SACf;KACF;IAAC,OAAO,KAAK,EAAE;QACd,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,MAAM,SAAS,GACb,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QAE9D,WAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE;YAChC,SAAS;YACT,SAAS;YACT,YAAY;YACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD,CAAC,CAAC;QAEH,WAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC1C,SAAS;YACT,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD,CAAC,CAAC;QAEH,8DAA8D;QAC9D,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE;YAC/C,MAAM,KAAK,CAAC;SACb;aAAM;YACL,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;SAChE;KACF;AACH,CAAC,CAAC;AAGO,8DAAyB"}