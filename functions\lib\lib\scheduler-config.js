"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSchedulerRegion = void 0;
const function_config_1 = require("./function-config");
/**
 * Helper to extract region from functionConfig in the correct format for scheduler functions
 * Ensures region is a string, not an array
 */
function getSchedulerRegion() {
    return Array.isArray(function_config_1.functionConfig.region)
        ? function_config_1.functionConfig.region[0]
        : function_config_1.functionConfig.region;
}
exports.getSchedulerRegion = getSchedulerRegion;
//# sourceMappingURL=scheduler-config.js.map