import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/components/providers/auth-provider";
import {
  collection,
  addDoc,
  query,
  orderBy,
  onSnapshot,
  Timestamp,
  doc,
  updateDoc,
  arrayUnion,
  getDoc,
  arrayRemove,
} from "firebase/firestore";
import { db } from "@/app/firebase";
import { Note } from "@/types/common";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { MessageSquare, Send, Loader2, User, MoreVertical, Pencil, Trash2 } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { formatDistanceToNow } from "date-fns";
import { de } from "date-fns/locale";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface NotesListProps {
  notes: Note[];
  isLoading: boolean;
  onEditNote: (noteId: string, content: string) => Promise<void>;
  onDeleteNote: (noteId: string) => Promise<void>;
  currentUserId: string | undefined;
}

const NotesList = ({ notes, isLoading, onEditNote, onDeleteNote, currentUserId }: NotesListProps) => {
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleEditClick = (note: Note) => {
    setEditingNoteId(note.id);
    setEditContent(note.content);
  };

  const handleSaveEdit = async () => {
    if (!editingNoteId) return;
    setIsSubmitting(true);
    try {
      await onEditNote(editingNoteId, editContent);
      setEditingNoteId(null);
    } catch (error) {
      console.error("Error saving edited note:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingNoteId(null);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
      </div>
    );
  }

  if (notes.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <MessageSquare className="h-10 w-10 mx-auto mb-2 opacity-50" />
        <p>Noch keine Notizen vorhanden.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {notes.map((note) => (
        <div key={note.id} className="bg-muted/40 p-4 rounded-lg">
          {editingNoteId === note.id ? (
            <div className="space-y-2">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="min-h-[100px]"
              />
              <div className="flex justify-end space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCancelEdit}
                  disabled={isSubmitting}
                >
                  Abbrechen
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveEdit}
                  disabled={!editContent.trim() || isSubmitting}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    "Speichern"
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarFallback className="bg-primary/20 text-primary">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium text-sm">{note.createdByUserName || "Nutzer"}</p>
                    <p className="text-xs text-muted-foreground">
                      {note.createdAt
                        ? formatDistanceToNow(
                            note.createdAt instanceof Timestamp
                              ? note.createdAt.toDate()
                              : typeof note.createdAt === 'object' && 'toDate' in note.createdAt && typeof note.createdAt.toDate === 'function'
                                ? note.createdAt.toDate()
                                : new Date(note.createdAt),
                            { addSuffix: true, locale: de }
                          )
                        : ""}
                    </p>
                  </div>
                </div>
                {note.createdByUserId === currentUserId && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                        <span className="sr-only">Optionen</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleEditClick(note)}>
                        <Pencil className="h-4 w-4 mr-2" />
                        Bearbeiten
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => onDeleteNote(note.id)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Löschen
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
              <p className="text-sm whitespace-pre-wrap">{note.content}</p>
            </>
          )}
        </div>
      ))}
    </div>
  );
};

interface NoteFormProps {
  onSubmit: (content: string) => Promise<void>;
  isSubmitting: boolean;
}

const NoteForm = ({ onSubmit, isSubmitting }: NoteFormProps) => {
  const [content, setContent] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) return;

    try {
      await onSubmit(content);
      setContent("");
    } catch (error) {
      console.error("Error submitting note:", error);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <Textarea
        placeholder="Schreiben Sie einen Kommentar..."
        value={content}
        onChange={(e) => setContent(e.target.value)}
        className="min-h-[100px] mb-2"
        disabled={isSubmitting}
      />
      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={!content.trim() || isSubmitting}
          size="sm"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Send className="h-4 w-4 mr-2" />
          )}
          Notiz hinzufügen
        </Button>
      </div>
    </form>
  );
};

interface NotesPanelProps {
  entityType: "object" | "unit" | "contract";
  entityId: string;
  objectId?: string; // Only needed for units
  hideHeader?: boolean; // New prop to control header visibility
}

export function NotesPanel({ entityType, entityId, objectId, hideHeader }: NotesPanelProps) {
  const { user, activeOrganizationId } = useAuth();
  const [notes, setNotes] = useState<Note[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const viewportRef = useRef<HTMLDivElement>(null); // Ref for the ScrollArea viewport

  useEffect(() => {
    if (!activeOrganizationId || !entityId) return;

    setIsLoading(true);
    let path = "";

    if (entityType === "object") {
      path = `organizations/${activeOrganizationId}/objects/${entityId}`;
    } else if (entityType === "unit" && objectId) {
      path = `organizations/${activeOrganizationId}/objects/${objectId}/units/${entityId}`;
    } else if (entityType === "contract") {
      path = `organizations/${activeOrganizationId}/contracts/${entityId}`;
    } else {
      console.error("Invalid entity type or missing objectId for unit");
      setIsLoading(false);
      return;
    }

    const docRef = doc(db, path);

    const unsubscribe = onSnapshot(docRef, (docSnapshot) => {
      if (docSnapshot.exists()) {
        const data = docSnapshot.data();
        const notesList = data.notesList || [];

        const sortedNotes = [...notesList].sort((a, b) => {
          const dateA = a.createdAt?.toDate?.() || new Date(a.createdAt);
          const dateB = b.createdAt?.toDate?.() || new Date(b.createdAt);
          return dateA.getTime() - dateB.getTime();
        });

        setNotes(sortedNotes);
      } else {
        setNotes([]);
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [activeOrganizationId, entityId, entityType, objectId]);

  // Scroll to bottom when notes change or after loading
  useEffect(() => {
    if (viewportRef.current) {
      requestAnimationFrame(() => {
        if (viewportRef.current) {
          viewportRef.current.scrollTop = viewportRef.current.scrollHeight;
        }
      });
    }
  }, [notes, isLoading]);

  const getEntityRef = () => {
    if (!activeOrganizationId || !entityId) {
      throw new Error("Missing organization or entity ID");
    }

    let path = "";
    if (entityType === "object") {
      path = `organizations/${activeOrganizationId}/objects/${entityId}`;
    } else if (entityType === "unit" && objectId) {
      path = `organizations/${activeOrganizationId}/objects/${objectId}/units/${entityId}`;
    } else if (entityType === "contract") {
      path = `organizations/${activeOrganizationId}/contracts/${entityId}`;
    } else {
      throw new Error("Invalid entity type or missing objectId for unit");
    }

    return doc(db, path);
  };

  const handleAddNote = async (content: string) => {
    if (!user || !activeOrganizationId || !entityId) {
      toast.error("Nicht autorisiert oder fehlende Daten");
      return;
    }

    setIsSubmitting(true);
    try {
      const entityRef = getEntityRef();

      const newNote: Omit<Note, "id"> = {
        content,
        createdAt: new Date(),
        createdByUserId: user.uid,
        createdByUserName: user.displayName || user.email || "Nutzer",
      };

      const noteWithId: Note = {
        ...newNote,
        id: crypto.randomUUID(),
      };

      await updateDoc(entityRef, {
        notesList: arrayUnion(noteWithId),
      });

      toast.success("Notiz erfolgreich hinzugefügt");
    } catch (error) {
      console.error("Error adding note:", error);
      toast.error("Fehler beim Hinzufügen der Notiz");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditNote = async (noteId: string, newContent: string) => {
    if (!user || !activeOrganizationId || !entityId) {
      toast.error("Nicht autorisiert oder fehlende Daten");
      return;
    }

    try {
      const entityRef = getEntityRef();

      // Find the note to update
      const noteToUpdate = notes.find(note => note.id === noteId);
      if (!noteToUpdate) {
        throw new Error("Notiz nicht gefunden");
      }

      // Remove the old note
      await updateDoc(entityRef, {
        notesList: arrayRemove(noteToUpdate)
      });

      // Add the updated note (with the same ID and other properties)
      const updatedNote: Note = {
        ...noteToUpdate,
        content: newContent,
      };

      await updateDoc(entityRef, {
        notesList: arrayUnion(updatedNote)
      });

      toast.success("Notiz erfolgreich aktualisiert");
    } catch (error) {
      console.error("Error updating note:", error);
      toast.error("Fehler beim Aktualisieren der Notiz");
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    if (!user || !activeOrganizationId || !entityId) {
      toast.error("Nicht autorisiert oder fehlende Daten");
      return;
    }

    try {
      const entityRef = getEntityRef();

      // Find the note to delete
      const noteToDelete = notes.find(note => note.id === noteId);
      if (!noteToDelete) {
        throw new Error("Notiz nicht gefunden");
      }

      // Remove the note
      await updateDoc(entityRef, {
        notesList: arrayRemove(noteToDelete)
      });

      toast.success("Notiz erfolgreich gelöscht");
    } catch (error) {
      console.error("Error deleting note:", error);
      toast.error("Fehler beim Löschen der Notiz");
    }
  };

  return (
    <div className="flex flex-col h-full">
      {!hideHeader && (
        <>
          <div className="flex items-center justify-between">
            <h3 className="text-lg sm:text-xl font-bold flex items-center">
              Notizen
            </h3>
            <span className="text-sm text-muted-foreground">
              {notes.length} Einträge
            </span>
          </div>
          <Separator className="my-2" />
        </>
      )}

      {/* Main content area */}
      <div className="flex-1 flex flex-col min-h-0">
        <ScrollArea className="flex-1 pr-4 max-h-[300px]" viewportRef={viewportRef}>
          <NotesList
            notes={notes}
            isLoading={isLoading}
            onEditNote={handleEditNote}
            onDeleteNote={handleDeleteNote}
            currentUserId={user?.uid}
          />
        </ScrollArea>

        {/* Input area fixed at bottom */}
        <div className="mt-4 pt-2 border-t">
          <NoteForm onSubmit={handleAddNote} isSubmitting={isSubmitting} />
        </div>
      </div>
    </div>
  );
}