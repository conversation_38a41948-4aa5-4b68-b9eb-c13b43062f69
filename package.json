{"name": "nextjs-firebase-boilerplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:emulators": "concurrently \"firebase emulators:start\" \"next dev --turbopack\"", "build": "next build", "start": "next start", "lint": "next lint", "test:unit": "jest --runInBand", "test:firebase": "firebase emulators:exec --only firestore,storage \"jest --runInBand --config jest.config.node.js\""}, "dependencies": {"@anthropic-ai/vertex-sdk": "^0.11.4", "@firebase/rules-unit-testing": "^4.0.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "firebase": "^11.6.0", "framer-motion": "^12.6.5", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.2", "esbuild": "^0.19.2", "eslint": "^9", "eslint-config-next": "15.2.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mockdate": "^3.0.5", "node-fetch": "^2.7.0", "tailwindcss": "^4", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5"}}