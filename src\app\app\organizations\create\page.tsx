"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/providers/auth-provider";
import { doc, setDoc, serverTimestamp, collection } from "firebase/firestore";
import { db, functions } from "@/app/firebase";
import { httpsCallable } from "firebase/functions";
import { Building2, Info, ArrowLeft } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Address } from "@/types";
import { motion } from "framer-motion";

// Animation variants for staggered animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.08
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 120,
      damping: 20
    }
  }
};

export default function CreateOrganizationPage() {
  const { user, refreshUserToken } = useAuth();
  const router = useRouter();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    contactEmail: "",
    iban: "",
    accountHolder: "",
    bic: "",
    bankName: "",
    address: {
      street: "",
      houseNumber: "",
      zipCode: "",
      city: "",
      country: "Deutschland", // Default country
      additionalInfo: ""
    } as Address
  });

  // Update form fields
  const updateField = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Update address field
  const updateAddressField = (field: keyof Address, value: string) => {
    setFormData(prev => ({
      ...prev,
      address: {
        ...prev.address,
        [field]: value
      }
    }));
  };

  // Create organization
  const createOrganization = async () => {
    if (!user) {
      toast.error("Sie müssen angemeldet sein, um eine Organisation zu erstellen");
      return;
    }

    if (!formData.name.trim()) {
      toast.warning("Der Name der Organisation ist erforderlich");
      return;
    }

    setIsSubmitting(true);

    try {
      // Create a new organization using Cloud Function
      const createOrg = httpsCallable(functions, 'createOrganization');

      const result = await createOrg({
        name: formData.name,
        contactEmail: formData.contactEmail || null,
        iban: formData.iban || null,
        accountHolder: formData.accountHolder || null,
        bic: formData.bic || null,
        bankName: formData.bankName || null,
        address: {
          street: formData.address.street || null,
          houseNumber: formData.address.houseNumber || null,
          zipCode: formData.address.zipCode || null,
          city: formData.address.city || null,
          country: formData.address.country || null,
          additionalInfo: formData.address.additionalInfo || null
        }
      });

      const responseData = result.data as any;

      if (responseData && responseData.success) {
        toast.success("Organisation wurde erfolgreich erstellt");

        // Token-Refresh erzwingen
        await refreshUserToken();

        // Navigate to the manage page for the new organization
        if (responseData.organizationId) {
          router.push(`/app/organizations/manage`);
        } else {
          router.push("/app/dashboard");
        }
      } else {
        const errorMessage = responseData?.message || "Ein Fehler ist aufgetreten";
        toast.error(`Fehler beim Erstellen der Organisation: ${errorMessage}`);
      }
    } catch (error: any) {
      console.error("Error creating organization:", error);
      let message = "Ein unbekannter Fehler ist aufgetreten";

      if (error.code === 'functions/unauthenticated') {
        message = "Authentifizierung fehlgeschlagen. Bitte neu anmelden";
      } else if (error.code === 'functions/permission-denied') {
        message = "Sie haben keine Berechtigung, eine Organisation zu erstellen";
      } else if (error.message) {
        message = error.message;
      }

      toast.error(`Fehler beim Erstellen der Organisation: ${message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="container mx-auto py-8 md:py-12 px-4 md:px-6"
    >
      <motion.div
        variants={itemVariants}
        className="flex flex-col space-y-2 mb-8 md:mb-10"
      >
        <div className="flex items-center">
          <Building2 className="h-6 w-6 text-primary mr-3" />
          <h1 className="text-3xl font-bold tracking-tight">Organisation erstellen</h1>
        </div>
        <p className="text-muted-foreground text-base pl-9">
          Erstellen Sie eine neue Organisation für Ihre Immobilienverwaltung.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
        <motion.div variants={itemVariants} className="md:col-span-2">
          <Card className="overflow-hidden border rounded-md bg-card/60">
            <CardHeader className="pb-3 border-b">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl">Organisationsdetails</CardTitle>
                  <CardDescription>Grundlegende Informationen über Ihre neue Organisation</CardDescription>
                </div>
                <div className="rounded-full p-1.5 bg-primary/10 text-primary">
                  <Info className="h-5 w-5" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="org-name" className="text-sm font-medium">Name *</Label>
                  <Input
                    id="org-name"
                    value={formData.name}
                    onChange={e => updateField('name', e.target.value)}
                    placeholder="Name der Organisation"
                    required
                    className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="org-email" className="text-sm font-medium">Kontakt E-Mail</Label>
                  <Input
                    id="org-email"
                    type="email"
                    value={formData.contactEmail}
                    onChange={e => updateField('contactEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                  />
                </div>

                {/* Payment details section */}
                <div className="pt-4 border-t">
                  <h3 className="text-base font-medium mb-4">Zahlungsdetails</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="org-account-holder" className="text-sm font-medium">Kontoinhaber</Label>
                      <Input
                        id="org-account-holder"
                        value={formData.accountHolder}
                        onChange={e => updateField('accountHolder', e.target.value)}
                        placeholder="Name des Kontoinhabers"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-iban" className="text-sm font-medium">IBAN</Label>
                      <Input
                        id="org-iban"
                        value={formData.iban}
                        onChange={e => updateField('iban', e.target.value)}
                        placeholder="DE00 0000 0000 0000 0000 00"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-bic" className="text-sm font-medium">BIC</Label>
                      <Input
                        id="org-bic"
                        value={formData.bic}
                        onChange={e => updateField('bic', e.target.value)}
                        placeholder="ABCDEFGHIJK"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-bank-name" className="text-sm font-medium">Bankname</Label>
                      <Input
                        id="org-bank-name"
                        value={formData.bankName}
                        onChange={e => updateField('bankName', e.target.value)}
                        placeholder="Name der Bank"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>
                  </div>
                </div>

                {/* Address section */}
                <div className="pt-4 border-t">
                  <h3 className="text-base font-medium mb-4">Adresse</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="org-street" className="text-sm font-medium">Straße</Label>
                      <Input
                        id="org-street"
                        value={formData.address.street}
                        onChange={e => updateAddressField('street', e.target.value)}
                        placeholder="Straße"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-house-number" className="text-sm font-medium">Hausnummer</Label>
                      <Input
                        id="org-house-number"
                        value={formData.address.houseNumber}
                        onChange={e => updateAddressField('houseNumber', e.target.value)}
                        placeholder="Nr."
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-zip" className="text-sm font-medium">PLZ</Label>
                      <Input
                        id="org-zip"
                        value={formData.address.zipCode}
                        onChange={e => updateAddressField('zipCode', e.target.value)}
                        placeholder="PLZ"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-city" className="text-sm font-medium">Stadt</Label>
                      <Input
                        id="org-city"
                        value={formData.address.city}
                        onChange={e => updateAddressField('city', e.target.value)}
                        placeholder="Stadt"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-country" className="text-sm font-medium">Land</Label>
                      <Input
                        id="org-country"
                        value={formData.address.country}
                        onChange={e => updateAddressField('country', e.target.value)}
                        placeholder="Land"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="org-additional" className="text-sm font-medium">Zusatz</Label>
                      <Input
                        id="org-additional"
                        value={formData.address.additionalInfo ?? ""}
                        onChange={e => updateAddressField('additionalInfo', e.target.value)}
                        placeholder="z.B. 2. Stock"
                        className="h-10 focus-visible:ring-offset-0 focus-visible:ring-1"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between pt-2 border-t">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Zurück
              </Button>

              <Button
                onClick={createOrganization}
                disabled={isSubmitting || !formData.name.trim()}
                className="gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Erstellen...
                  </>
                ) : (
                  <>
                    Organisation erstellen
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  );
}