import fetch from 'node-fetch';

if (!globalThis.fetch) {
  globalThis.fetch = fetch as any; // Make fetch globally available
}

import '@testing-library/jest-dom'
import 'src/__tests__/test-utils'
import React from 'react'

// NextJS Image-Mock global einrichten
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    return React.createElement('img', {
      src: props.src,
      alt: props.alt,
      className: props.className,
    });
  },
})); 