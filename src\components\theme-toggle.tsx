"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"

export function ThemeToggle({ className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const { theme, setTheme } = useTheme()
  
  return (
    <button
      type="button"
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
      className={cn(
        "inline-flex items-center rounded-full border p-0.5",
        "cursor-pointer",
        className
      )}
      aria-label="Toggle Theme"
      {...props}
    >
      <Sun className="size-7 rounded-full p-1.5 bg-accent text-accent-foreground dark:bg-transparent dark:text-muted-foreground" />
      <Moon className="size-7 rounded-full p-1.5 text-muted-foreground dark:bg-accent dark:text-accent-foreground" />
    </button>
  )
}