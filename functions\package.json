{"name": "functions", "scripts": {"lint": "eslint \"src/**/*\"", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@anthropic-ai/vertex-sdk": "^0.11.4", "@genkit-ai/firebase": "^1.4.0", "@genkit-ai/googleai": "^1.4.0", "@genkit-ai/vertexai": "^1.10.0", "@sparticuz/chromium": "^121.0.0", "cors": "^2.8.5", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "npm": "^11.3.0", "puppeteer-core": "^21.5.2", "stripe": "^18.0.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "esbuild": "^0.25.3", "eslint": "^8.57.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}