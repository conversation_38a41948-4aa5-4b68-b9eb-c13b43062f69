import { db } from "@/app/firebase";
import { collection, addDoc, serverTimestamp } from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";

// Rule: Use TypeScript for all code with interfaces over types
interface ErrorEvent {
  errorId: string;
  message: string;
  stack?: string | null;
  componentName?: string;
  path: string;
  url: string;
  userAgent: string;
  timestamp: any; // Will be replaced with serverTimestamp
  metadata?: Record<string, any>;
}

interface ErrorContextData {
  componentName?: string;
  metadata?: Record<string, any>;
}

// Rule: Favor named exports for utilities
export const errorTrackingService = {
  errorSessionId: uuidv4(),
  contextData: {} as ErrorContextData,

  async initialize(): Promise<void> {
    if (typeof window === "undefined") return;

    // Set up global error listeners
    this.setupWindowErrorHandler();
    this.setupPromiseRejectionHandler();
    this.setupReactErrorHandler();

    console.log("Error tracking initialized with session:", this.errorSessionId);
  },

  // Rule: Use explicit return types for all functions
  setErrorContext(contextData: ErrorContextData): void {
    this.contextData = {
      ...this.contextData,
      ...contextData,
    };
  },

  // Rule: Provide proper error handling for logging
  // Rule: Discriminated union for error type
  async captureException(error: Error | string, additionalMetadata?: Record<string, any>): Promise<string> {
    if (process.env.NODE_ENV === "development") {
      console.error("Error captured:", error);
      // YAGNI: Skip tracking in development mode
      return uuidv4();
    }

    const errorId = uuidv4();
    const errorMessage = typeof error === "string" ? error : error.message;
    // KISS: Ensure stack is either a string or null (not undefined) for Firestore
    const errorStack = typeof error === "string" ? null : (error.stack || null);

    try {
      if (typeof window !== "undefined") {
        const errorEvent: Omit<ErrorEvent, "timestamp"> = {
          errorId,
          message: errorMessage,
          stack: errorStack,
          componentName: this.contextData.componentName,
          path: window.location.pathname,
          url: window.location.href,
          userAgent: navigator.userAgent,
          metadata: {
            ...this.contextData.metadata,
            ...additionalMetadata,
            errorSessionId: this.errorSessionId,
          },
        };

        await this.trackError(errorEvent);
      }
      
      return errorId;
    } catch (trackingError) {
      // Prevent infinite error loops
      console.error("Failed to track error:", trackingError);
      return errorId;
    }
  },

  // Rule: Use "function" keyword for pure functions
  setupWindowErrorHandler(): void {
    if (typeof window === "undefined") return;

    window.onerror = (message, source, lineno, colno, error) => {
      const errorInfo = {
        type: "window.onerror",
        source,
        lineno,
        colno,
      };
      
      this.captureException(
        error || String(message),
        errorInfo
      );
      
      // Don't prevent default error handling
      return false;
    };
  },

  setupPromiseRejectionHandler(): void {
    if (typeof window === "undefined") return;

    window.addEventListener("unhandledrejection", (event) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
      
      this.captureException(error, {
        type: "unhandledrejection",
      });
    });
  },

  setupReactErrorHandler(): void {
    // This is handled through error boundaries
    // See ErrorBoundary component
  },

  async trackError(errorEvent: Omit<ErrorEvent, "timestamp">): Promise<void> {
    try {
      // DRY + MaintainerFocus + LeastAstonishment: Recursively sanitize objects to prevent undefined values
      const sanitizeData = (obj: any): any => {
        if (obj === undefined) return null;
        if (obj === null) return null;
        if (typeof obj !== 'object') return obj;
        
        if (Array.isArray(obj)) {
          return obj.map(item => sanitizeData(item));
        }
        
        return Object.entries(obj).reduce((acc, [key, value]) => {
          acc[key] = sanitizeData(value);
          return acc;
        }, {} as Record<string, any>);
      };

      const sanitizedEvent = sanitizeData(errorEvent);

      await addDoc(collection(db, "error_events"), {
        ...sanitizedEvent,
        timestamp: serverTimestamp(),
      });
    } catch (error) {
      console.error("Failed to save error event:", error);
    }
  },
}; 