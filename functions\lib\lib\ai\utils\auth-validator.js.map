{"version": 3, "file": "auth-validator.js", "sourceRoot": "", "sources": ["../../../../src/lib/ai/utils/auth-validator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAAyD;AACzD,8CAA+C;AAC/C,sDAAwC;AAExC;;GAEG;AACH,SAAgB,cAAc,CAAC,UAAmB;IAChD,OAAO,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC;QACnD,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,IAAI,CAAC;AACX,CAAC;AAJD,wCAIC;AAED;;GAEG;AACI,KAAK,UAAU,YAAY,CAAC,OAAe;IAChD,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,4BAA4B,CAAC,CAAC;KACvE;IAED,IAAI;QACF,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAE/D,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;YAChC,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,8BAA8B,CAAC,CAAC;SACzE;QAED,OAAO,YAAY,CAAC;KACrB;IAAC,OAAO,KAAK,EAAE;QACd,WAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/D,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,8BAA8B,CAAC,CAAC;KACzE;AACH,CAAC;AAjBD,oCAiBC"}