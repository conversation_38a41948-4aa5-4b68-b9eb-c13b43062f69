{"version": 3, "file": "fraudPrevention.js", "sourceRoot": "", "sources": ["../../src/services/fraudPrevention.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sDAAwC;AACxC,oCAA2C;AAC3C,wDAAqD;AAErD;;;;GAIG;AACH,MAAa,sBAAsB;IAKjC;QAHiB,eAAU,GAAG,eAAe,CAAC;QAC7B,sBAAiB,GAAG,GAAG,CAAC,CAAC,qBAAqB;QAG7D,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACK,aAAa,CAAC,KAAa;QACjC,6CAA6C;QAC7C,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAEnD,kFAAkF;QAClF,iDAAiD;QACjD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,wBAAwB,CAAC,KAAa;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAExC,0CAA0C;QAC1C,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEhE,uCAAuC;QACvC,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YACvD,KAAK;YACL,SAAS,EAAE,IAAA,uBAAe,GAAE;YAC5B,SAAS,EAAE,qBAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;SACzC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,gBAAgB,CAAC,KAAa;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAExC,oCAAoC;QACpC,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;QACvE,OAAO,GAAG,CAAC,MAAM,CAAC;IACpB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,kBAAkB;QACtB,MAAM,GAAG,GAAG,qBAAS,CAAC,GAAG,EAAE,CAAC;QAE5B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;aACvD,KAAK,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,CAAC;aAC5B,GAAG,EAAE,CAAC;QAET,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC1B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACtB,KAAK,EAAE,CAAC;QACV,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;SACtB;QAED,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA9ED,wDA8EC"}