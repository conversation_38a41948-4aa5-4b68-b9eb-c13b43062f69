import { z } from "genkit";

// Import existing UserStory schema
import { UserStorySchema, UserStory } from "./story-schema";

// Schema for single story response (existing behavior)
const SingleStoryResponse = z.object({
  isMultiple: z.boolean().refine(val => val === false, {
    message: "isMultiple must be false for SingleStoryResponse"
  }),
  story: UserStorySchema,
});

// Schema for multiple stories response
const MultipleStoriesResponse = z.object({
  isMultiple: z.boolean().refine(val => val === true, {
    message: "isMultiple must be true for MultipleStoriesResponse"
  }),
  stories: z.array(UserStorySchema).min(2).describe("Array of user stories extracted from multi-point feedback"),
  summary: z.string().describe("Brief summary of how the feedback was split into multiple stories"),
});

// Union schema that can handle both single and multiple stories
export const StoryResponseSchema = z.union([
  SingleStoryResponse,
  MultipleStoriesResponse,
]);

// Type definitions
export type SingleStoryResponse = z.infer<typeof SingleStoryResponse>;
export type MultipleStoriesResponse = z.infer<typeof MultipleStoriesResponse>;
export type StoryResponse = z.infer<typeof StoryResponseSchema>;

// Helper type guards
export function isSingleStoryResponse(response: StoryResponse): response is SingleStoryResponse {
  return !response.isMultiple;
}

export function isMultipleStoriesResponse(response: StoryResponse): response is MultipleStoriesResponse {
  return response.isMultiple;
}

// Export individual story type for compatibility
export type { UserStory };
