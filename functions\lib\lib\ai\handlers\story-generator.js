"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateUserStoryFunction = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const v2_1 = require("firebase-functions/v2");
const genkit_config_1 = require("../config/genkit-config");
const story_schema_1 = require("../schemas/story-schema");
const story_prompts_1 = require("../prompts/story-prompts");
/**
 * Helper function to validate and debug schema validation issues
 */
function validateAndDebugSchema(rawData, requestId, userId) {
    try {
        // Attempt to parse with the schema
        const validatedData = story_schema_1.UserStorySchema.parse(rawData);
        v2_1.logger.debug("Schema validation successful", {
            requestId,
            userId,
            validatedFields: Object.keys(validatedData),
            isRejected: validatedData.isRejected,
        });
        return validatedData;
    }
    catch (validationError) {
        // Log detailed validation error information
        v2_1.logger.error("Schema validation failed", {
            requestId,
            userId,
            validationError: validationError.message,
            validationIssues: validationError.issues || [],
            rawDataKeys: rawData ? Object.keys(rawData) : "null",
            rawDataTypes: rawData ? Object.entries(rawData).map(([key, value]) => ({ key, type: typeof value })) : "null",
            rawDataSample: rawData ? JSON.stringify(rawData, null, 2) : "null",
        });
        // Re-throw the validation error
        throw validationError;
    }
}
/**
 * Transforms user feedback into a structured user story
 */
// Instead of exporting the function directly, create it to be exported from index.ts
const generateUserStoryFunction = async (data, context) => {
    var _a;
    const requestId = Date.now().toString();
    v2_1.logger.debug("Story generation started", {
        requestId,
        functionName: "generateUserStoryFunction",
    });
    try {
        // Authentication is automatically handled by onCall
        if (!context || !context.auth) {
            v2_1.logger.warn("Authentication missing", { requestId });
            throw new functions.https.HttpsError("unauthenticated", "Authentication required");
        }
        const userId = context.auth.uid;
        v2_1.logger.debug("User authenticated", { requestId, userId });
        // Basic validation
        if (!data.feedbackText || typeof data.feedbackText !== "string") {
            v2_1.logger.warn("Invalid feedback text", {
                requestId,
                userId,
                feedbackProvided: !!data.feedbackText,
            });
            throw new functions.https.HttpsError("invalid-argument", "Feedback text is required");
        }
        const projectContext = data.projectContext || "";
        const issuesContext = data.issuesContext || "";
        v2_1.logger.debug("Input data validated", {
            requestId,
            userId,
            feedbackLength: data.feedbackText.length,
            feedbackPreview: data.feedbackText.substring(0, 100) +
                (data.feedbackText.length > 100 ? "..." : ""),
            projectContextLength: projectContext.length,
            issuesContextLength: issuesContext.length,
        });
        v2_1.logger.info("Generating user story from feedback", {
            requestId,
            userId,
            hasProjectContext: !!projectContext,
            hasIssuesContext: !!issuesContext,
        });
        v2_1.logger.debug("Creating prompt", { requestId, userId });
        const prompt = (0, story_prompts_1.createStoryPrompt)(data.feedbackText, projectContext, issuesContext);
        v2_1.logger.debug("Prompt created", {
            requestId,
            userId,
            promptLength: typeof prompt === "string" ? prompt.length : "complex-prompt-object",
            promptPreview: typeof prompt === "string" ? prompt.substring(0, 200) + (prompt.length > 200 ? "..." : "") : "complex-prompt-object",
        });
        // Debug: Log the full prompt for debugging
        v2_1.logger.debug("Full prompt content", {
            requestId,
            userId,
            fullPrompt: prompt,
        });
        v2_1.logger.debug("Calling AI model", {
            requestId,
            userId,
            model: genkit_config_1.mainModel || "unknown-model",
            temperature: 0.7,
            maxOutputTokens: 1024,
        });
        let rawOutput;
        try {
            const result = await genkit_config_1.ai.generate({
                model: genkit_config_1.mainModel,
                prompt,
                output: { schema: story_schema_1.UserStorySchema },
                config: {
                    temperature: 0.7,
                    maxOutputTokens: 1024,
                },
            });
            rawOutput = result.output;
            // Debug: Log raw AI response before schema validation
            v2_1.logger.debug("Raw AI response received", {
                requestId,
                userId,
                rawOutput: JSON.stringify(rawOutput, null, 2),
                outputType: typeof rawOutput,
                outputKeys: rawOutput ? Object.keys(rawOutput) : "null",
                outputStringified: rawOutput ? JSON.stringify(rawOutput) : "null",
            });
            if (!rawOutput) {
                throw new Error("AI model returned empty output");
            }
            // Use the validation helper to validate and debug schema issues
            const userStory = validateAndDebugSchema(rawOutput, requestId, userId);
            v2_1.logger.debug("AI response received", {
                requestId,
                userId,
                responseReceived: !!rawOutput,
                storyTitle: userStory.title,
                storyLength: JSON.stringify(userStory).length,
                storyFields: Object.keys(userStory),
            });
            v2_1.logger.info("User story generated successfully", {
                requestId,
                userId,
                storyTitle: userStory.title,
            });
            return rawOutput;
        }
        catch (aiError) {
            // Log AI-specific errors with more detail
            const aiErrorMessage = aiError instanceof Error ? aiError.message : String(aiError);
            v2_1.logger.error("AI generation error in story generation", {
                requestId,
                userId: ((_a = context.auth) === null || _a === void 0 ? void 0 : _a.uid) || "unknown",
                aiError: aiErrorMessage,
                aiErrorType: aiError instanceof Error ? aiError.constructor.name : "Unknown",
                aiErrorStack: aiError instanceof Error ? aiError.stack : undefined,
                rawOutput: rawOutput ? JSON.stringify(rawOutput, null, 2) : "null",
                promptLength: typeof prompt === "string" ? prompt.length : 0,
            });
            throw aiError;
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const errorType = error instanceof Error ? error.constructor.name : "Unknown";
        v2_1.logger.debug("Exception details", {
            requestId,
            errorType,
            errorMessage,
            stack: error instanceof Error ? error.stack : undefined,
        });
        v2_1.logger.error("Error generating user story", {
            requestId,
            error: errorMessage,
            stack: error instanceof Error ? error.stack : undefined,
        });
        // onCall automatically handles error forwarding to the client
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        else {
            throw new functions.https.HttpsError("internal", errorMessage);
        }
    }
};
exports.generateUserStoryFunction = generateUserStoryFunction;
//# sourceMappingURL=story-generator.js.map