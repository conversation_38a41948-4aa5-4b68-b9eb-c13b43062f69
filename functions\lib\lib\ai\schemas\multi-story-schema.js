"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isMultipleStoriesResponse = exports.isSingleStoryResponse = exports.StoryResponseSchema = void 0;
const genkit_1 = require("genkit");
// Import existing UserStory schema
const story_schema_1 = require("./story-schema");
// Schema for single story response (existing behavior)
const SingleStoryResponse = genkit_1.z.object({
    isMultiple: genkit_1.z.boolean().refine(val => val === false, {
        message: "isMultiple must be false for SingleStoryResponse"
    }),
    story: story_schema_1.UserStorySchema,
});
// Schema for multiple stories response
const MultipleStoriesResponse = genkit_1.z.object({
    isMultiple: genkit_1.z.boolean().refine(val => val === true, {
        message: "isMultiple must be true for MultipleStoriesResponse"
    }),
    stories: genkit_1.z.array(story_schema_1.UserStorySchema).min(2).describe("Array of user stories extracted from multi-point feedback"),
    summary: genkit_1.z.string().describe("Brief summary of how the feedback was split into multiple stories"),
});
// Union schema that can handle both single and multiple stories
exports.StoryResponseSchema = genkit_1.z.union([
    SingleStoryResponse,
    MultipleStoriesResponse,
]);
// Helper type guards
function isSingleStoryResponse(response) {
    return !response.isMultiple;
}
exports.isSingleStoryResponse = isSingleStoryResponse;
function isMultipleStoriesResponse(response) {
    return response.isMultiple;
}
exports.isMultipleStoriesResponse = isMultipleStoriesResponse;
//# sourceMappingURL=multi-story-schema.js.map