import { cn, formatDate } from "../utils";

describe("cn function", () => {
  test("should merge class names correctly", () => {
    // Normal case
    expect(cn("text-red-500", "bg-blue-500")).toBe("text-red-500 bg-blue-500");
    expect(cn("p-4", { "hidden": true, "block": false })).toBe("p-4 hidden");
    expect(cn("m-2", ["px-4", "py-2"])).toBe("m-2 px-4 py-2");
  });

  test("should handle edge cases", () => {
    // Empty inputs
    expect(cn()).toBe("");
    expect(cn("")).toBe("");
    expect(cn([])).toBe("");
    expect(cn({})).toBe("");
    
    // Mixed valid and empty inputs
    expect(cn("text-lg", "", null, undefined, "p-2")).toBe("text-lg p-2");
  });

  test("should handle conflicting class names", () => {
    // Tailwind classes that conflict - latter ones should override
    expect(cn("p-2", "p-4")).toBe("p-4");
    expect(cn("text-sm text-red-500", "text-blue-500")).toBe("text-sm text-blue-500");
    expect(cn("m-1", { "m-2": true })).toBe("m-2");
  });
});

describe("formatDate function", () => {
  test("should format date correctly", () => {
    // Normal case - fixed date for consistent testing
    const testDate = new Date(2023, 0, 15, 14, 30); // Jan 15, 2023, 14:30
    const formattedDate = formatDate(testDate);
    
    // Ensure contains date parts and time
    expect(formattedDate).toContain("15");
    expect(formattedDate).toContain("2023");
    expect(formattedDate).toContain("14:30");
  });

  test("should handle edge case dates", () => {
    // Year 1970
    const oldDate = new Date(0); // Jan 1, 1970, 00:00:00 UTC
    expect(formatDate(oldDate)).toMatch(/\d{2}:\d{2}/);
    
    // Future date
    const futureDate = new Date(2099, 11, 31, 23, 59);
    expect(formatDate(futureDate)).toMatch(/2099.+23:59/);
    
    // Leap year
    const leapYearDate = new Date(2024, 1, 29, 12, 0);
    expect(formatDate(leapYearDate)).toMatch(/2024.+12:00/);
  });

  test("should throw error for invalid date input", () => {
    // Invalid date (results in "Invalid Date")
    const invalidDate = new Date("invalid-date");
    expect(() => formatDate(invalidDate)).toThrow(RangeError);
    
    // Test with explicitly passing invalid values
    // @ts-expect-error - intentionally testing wrong type
    expect(() => formatDate(null)).toThrow(TypeError);
    // @ts-expect-error - intentionally testing wrong type
    expect(() => formatDate(undefined)).toThrow(TypeError);
    // @ts-expect-error - intentionally testing wrong type
    expect(() => formatDate("2023-01-15")).toThrow(TypeError);
  });
});
