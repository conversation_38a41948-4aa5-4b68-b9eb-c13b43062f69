{"version": 3, "file": "multi-story-schema.js", "sourceRoot": "", "sources": ["../../../../src/lib/ai/schemas/multi-story-schema.ts"], "names": [], "mappings": ";;;AAAA,mCAA2B;AAE3B,mCAAmC;AACnC,iDAA4D;AAE5D,uDAAuD;AACvD,MAAM,mBAAmB,GAAG,UAAC,CAAC,MAAM,CAAC;IACnC,UAAU,EAAE,UAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,KAAK,EAAE;QACnD,OAAO,EAAE,kDAAkD;KAC5D,CAAC;IACF,KAAK,EAAE,8BAAe;CACvB,CAAC,CAAC;AAEH,uCAAuC;AACvC,MAAM,uBAAuB,GAAG,UAAC,CAAC,MAAM,CAAC;IACvC,UAAU,EAAE,UAAC,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,EAAE;QAClD,OAAO,EAAE,qDAAqD;KAC/D,CAAC;IACF,OAAO,EAAE,UAAC,CAAC,KAAK,CAAC,8BAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,2DAA2D,CAAC;IAC9G,OAAO,EAAE,UAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mEAAmE,CAAC;CAClG,CAAC,CAAC;AAEH,gEAAgE;AACnD,QAAA,mBAAmB,GAAG,UAAC,CAAC,KAAK,CAAC;IACzC,mBAAmB;IACnB,uBAAuB;CACxB,CAAC,CAAC;AAOH,qBAAqB;AACrB,SAAgB,qBAAqB,CAAC,QAAuB;IAC3D,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC9B,CAAC;AAFD,sDAEC;AAED,SAAgB,yBAAyB,CAAC,QAAuB;IAC/D,OAAO,QAAQ,CAAC,UAAU,CAAC;AAC7B,CAAC;AAFD,8DAEC"}