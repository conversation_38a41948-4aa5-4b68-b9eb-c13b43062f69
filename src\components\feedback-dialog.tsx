"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { addDoc, collection } from "firebase/firestore";
import { db, auth } from "@/app/firebase";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface FeedbackDialogProps {
  trigger: React.ReactNode;
}

export function FeedbackDialog({ trigger }: FeedbackDialogProps) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedType, setSelectedType] = useState<string>("");
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const formRef = useRef<HTMLFormElement>(null);

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setIsLoading(true);

    if (!auth.currentUser) {
      toast.error("Sie müssen angemeldet sein, um Feedback zu senden.");
      return;
    }

    const formData = new FormData(event.currentTarget);
    const feedback = {
      type: formData.get("type") as string,
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      userId: auth.currentUser.uid,
      createdAt: new Date(),
    };

    try {
      await addDoc(collection(db, "feedback"), feedback);
      toast.success("Vielen Dank für Ihr Feedback!");
      formRef.current?.reset();
      setSelectedType("");
      setIsOpen(false);
    } catch (error) {
      console.error("Fehler beim Senden des Feedbacks:", error);
      toast.error(
        "Beim Senden ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut."
      );
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Feedback</DialogTitle>
          <DialogDescription>
            Teilen Sie uns mit, wie wir die Software verbessern können.
          </DialogDescription>
        </DialogHeader>
        <form ref={formRef} onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="type">Feedback-Typ</Label>
            <Select 
              name="type" 
              required 
              value={selectedType}
              onValueChange={setSelectedType}
            >
              <SelectTrigger>
                <SelectValue placeholder="Wählen Sie einen Typ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bug">Fehler melden</SelectItem>
                <SelectItem value="feature">Feature-Vorschlag</SelectItem>
                <SelectItem value="improvement">
                  Verbesserungsvorschlag
                </SelectItem>
                <SelectItem value="other">Sonstiges</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="title">Titel</Label>
            <Input
              id="title"
              name="title"
              placeholder="Kurze Zusammenfassung"
              required
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="description">Beschreibung</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Beschreiben Sie Ihr Anliegen detailliert"
              required
              className="h-32"
            />
          </div>
          <DialogFooter>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Wird gesendet..." : "Feedback senden"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 